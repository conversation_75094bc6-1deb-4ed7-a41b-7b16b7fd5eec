import 'package:flutter/material.dart';
import 'package:du_an_flutter/model/note.dart';
import 'package:du_an_flutter/services/notes_service.dart';

class NoteOverlayWidget extends StatefulWidget {
  final List<Note> notes;
  final VoidCallback onClose;
  final Function(Note)? onNoteDetail; // Callback để navigate đến note detail
  final VoidCallback? onTemporaryDismiss; // Callback for background tap

  const NoteOverlayWidget({
    Key? key,
    required this.notes,
    required this.onClose,
    this.onNoteDetail,
    this.onTemporaryDismiss,
  }) : super(key: key);

  @override
  State<NoteOverlayWidget> createState() => _NoteOverlayWidgetState();
}

class _NoteOverlayWidgetState extends State<NoteOverlayWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _isClosing = false;

  @override
  void initState() {
    super.initState();

    // Animation setup
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    // Start animation
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // Close overlay with animation and mark all notes as permanently read
  Future<void> _closeOverlay() async {
    if (_isClosing) return;

    setState(() {
      _isClosing = true;
    });

    // Mark overlay as permanently closed (close button pressed)
    NotesService.markOverlayPermanentlyClosed(widget.notes);

    await _animationController.reverse();
    widget.onClose();
  }

  // Handle background tap to temporarily dismiss overlay
  Future<void> _handleBackgroundTap() async {
    if (_isClosing) return;

    setState(() {
      _isClosing = true;
    });

    // Mark overlay as temporarily dismissed (background tap)
    NotesService.markOverlayTemporarilyDismissed(widget.notes);

    await _animationController.reverse();

    // Call temporary dismiss callback if provided
    if (widget.onTemporaryDismiss != null) {
      widget.onTemporaryDismiss!();
    } else {
      widget.onClose();
    }
  }

  // Handle note detail navigation (also permanently closes overlay)
  void _handleNoteDetail(Note note) {
    // Mark overlay as permanently closed (detail view accessed)
    NotesService.markOverlayPermanentlyClosed(widget.notes);

    if (widget.onNoteDetail != null) {
      widget.onNoteDetail!(note);
    }
  }

  // Build simplified note item for list display
  Widget _buildNoteItem(Note note, int index) {
    // Check if this note is due today for special highlighting
    final bool isDueToday = NotesService.isNoteDueToday(note);

    return Container(
      margin: EdgeInsets.only(
        left: 20,
        right: 20,
        top: index == 0 ? 20 : 8,
        bottom: index == widget.notes.length - 1 ? 20 : 8,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        // Add special border for today's notes
        border: isDueToday
            ? Border.all(color: Colors.orange[600]!, width: 3)
            : null,
        boxShadow: [
          BoxShadow(
            color: isDueToday
                ? Colors.orange.withOpacity(0.3)
                : Colors.black.withOpacity(0.1),
            spreadRadius: isDueToday ? 2 : 1,
            blurRadius: isDueToday ? 12 : 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with special highlighting for today's notes
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDueToday
                    ? [
                        Colors.orange[600]!,
                        Colors.orange[500]!,
                      ]
                    : [
                        Colors.red[600]!,
                        Colors.red[500]!,
                      ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  isDueToday ? Icons.today : Icons.notifications_active,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        note.title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          height: 1.3,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (isDueToday) ...[
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.9),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'HÔM NAY',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: isDueToday
                                  ? Colors.orange[700]
                                  : Colors.red[700],
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    '#${note.id}',
                    style: const TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Description content
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: Text(
              note.description,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
                height: 1.5,
              ),
              maxLines: 4,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // Action button for note detail
          if (widget.onNoteDetail != null)
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () {
                    _handleNoteDetail(note);
                  },
                  icon: const Icon(Icons.visibility, size: 16),
                  label: const Text(
                    'Xem chi tiết',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        isDueToday ? Colors.orange[50] : Colors.red[50],
                    foregroundColor:
                        isDueToday ? Colors.orange[700] : Colors.red[700],
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                      side: BorderSide(
                        color:
                            isDueToday ? Colors.orange[300]! : Colors.red[300]!,
                        width: 1,
                      ),
                    ),
                    elevation: 0,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Build header with note count
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          const Icon(
            Icons.notifications_active,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Thông báo ghi chú chưa đọc',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              '${widget.notes.length} ghi chú',
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build action button
  Widget _buildActionButton() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: _closeOverlay,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.white,
            foregroundColor: Colors.red[600],
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: const Text(
            'Đã đọc tất cả',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Material(
            color: Colors.transparent,
            child: GestureDetector(
                onTap: _handleBackgroundTap,
                child: Container(
                  color: Colors.black.withOpacity(0.8 * _fadeAnimation.value),
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: FadeTransition(
                      opacity: _fadeAnimation,
                      child: Center(
                        child: GestureDetector(
                          onTap:
                              () {}, // Prevent background tap when tapping on content
                          child: Container(
                            constraints: BoxConstraints(
                              maxHeight:
                                  MediaQuery.of(context).size.height * 0.85,
                              maxWidth: MediaQuery.of(context).size.width * 0.9,
                            ),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  Colors.red[600]!,
                                  Colors.red[500]!,
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.3),
                                  spreadRadius: 2,
                                  blurRadius: 15,
                                  offset: const Offset(0, 8),
                                ),
                              ],
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // Header
                                _buildHeader(),

                                // Scrollable list of notes
                                Flexible(
                                  child: Container(
                                    decoration: const BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.only(
                                        bottomLeft: Radius.circular(20),
                                        bottomRight: Radius.circular(20),
                                      ),
                                    ),
                                    child: Column(
                                      children: [
                                        // Notes list
                                        Flexible(
                                          child: ListView.builder(
                                            shrinkWrap: true,
                                            itemCount: widget.notes.length,
                                            itemBuilder: (context, index) {
                                              return _buildNoteItem(
                                                  widget.notes[index], index);
                                            },
                                          ),
                                        ),

                                        // Action button
                                        _buildActionButton(),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                )));
      },
    );
  }
}
