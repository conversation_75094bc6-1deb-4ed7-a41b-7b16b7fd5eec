import 'dart:convert';
import 'package:du_an_flutter/APIconfig/api_config.dart';
import 'package:du_an_flutter/APIconfig/api_endpoints.dart';
import 'package:du_an_flutter/constants/colors.dart';
import 'package:du_an_flutter/l10n/app_localizations.dart';
import 'package:du_an_flutter/model/activity_item.dart';
import 'package:du_an_flutter/model/currency.dart';
import 'package:du_an_flutter/page/pageHome.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:file_picker/file_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dio/dio.dart';
import 'dart:async';

// Model cho gợi ý ngân hàng
class BankSuggestion {
  final String id;
  final String bankName;
  final String bankNumber;
  final String accountHolder;

  BankSuggestion({
    required this.id,
    required this.bankName,
    required this.bankNumber,
    required this.accountHolder,
  });

  factory BankSuggestion.fromJson(Map<String, dynamic> json) {
    return BankSuggestion(
      id: json['id'] ?? '',
      bankName: json['bankName'] ?? '',
      bankNumber: json['bankNumber'] ?? '',
      accountHolder: json['accountHolder'] ?? '',
    );
  }
}

// Hàm định dạng tiền tệ VND
String formatCurrencyVND(String input) {
  final digits = input.replaceAll(RegExp(r'[^\d]'), '');
  if (digits.isEmpty) return '';
  final buffer = StringBuffer();
  int count = 0;
  for (int i = digits.length - 1; i >= 0; i--) {
    buffer.write(digits[i]);
    count++;
    if (count == 3 && i != 0) {
      buffer.write('.');
      count = 0;
    }
  }
  return buffer.toString().split('').reversed.join('');
}

// Formatter để định dạng số tiền khi nhập
class ThousandsSeparatorInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    final digitsOnly = newValue.text.replaceAll(RegExp(r'[^\d]'), '');

    if (digitsOnly.isEmpty) {
      return newValue.copyWith(text: '');
    }

    final formatted = formatCurrencyVND(digitsOnly);

    // Tính toán lại vị trí con trỏ để nó không bị nhảy khi định dạng
    int selectionIndex =
        formatted.length - (digitsOnly.length - newValue.selection.end);

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(
        offset: selectionIndex < 0 ? 0 : selectionIndex,
      ),
    );
  }
}

class PaymentForm extends StatefulWidget {
  final ActivityItem? editItem; // Tham số để nhận item cần chỉnh sửa
  final String title; // Tiêu đề của form
  final VoidCallback? onSubmitSuccess; // Callback khi submit thành công

  const PaymentForm({
    super.key,
    this.editItem, // Tham số tùy chọn cho chế độ chỉnh sửa
    this.title = 'Payment Form', // Default title - will be localized in UI
    this.onSubmitSuccess,
  });

  @override
  State<PaymentForm> createState() => _PaymentFormState();
}

class _PaymentFormState extends State<PaymentForm> {
  final _formKey = GlobalKey<FormState>();
  bool showSwiftCode = false;
  bool isLoading = true; // Trạng thái tải currencies
  bool _isSubmitting = false; // Trạng thái đang gửi form
  bool _isSearching = false; // Trạng thái đang tìm kiếm gợi ý
  bool _suggestionSelected = false; // Cờ đánh dấu đã chọn gợi ý
  bool _hasChanges = false; // Cờ đánh dấu người dùng đã thay đổi dữ liệu

  // Controllers cho các trường trong form
  final _beneficiaryController = TextEditingController();
  final _accountNumberController = TextEditingController();
  final _bankController = TextEditingController();
  final _swiftCodeController = TextEditingController();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _titleController = TextEditingController();

  // Lưu giá trị ban đầu để kiểm tra sự thay đổi
  String _initialBeneficiary = '';
  String _initialAccount = '';
  String _initialBank = '';
  String _initialSwiftCode = '';
  String _initialAmount = '';
  String _initialDescription = '';
  String _initialTitle = '';
  Currency? _initialCurrency;
  int _initialFileCount = 0; // Track initial file count

  List<Currency> currencies = [];
  Currency? selectedCurrency;
  List<PlatformFile> _selectedFiles = []; // Danh sách các file đính kèm
  List<BankSuggestion> _bankSuggestions = []; // Danh sách gợi ý ngân hàng
  Timer? _debounce; // Debounce timer cho tìm kiếm
  final FocusNode _beneficiaryFocusNode =
      FocusNode(); // Focus node cho trường tên người thụ hưởng

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  // Initialize form with proper async handling
  Future<void> _initializeForm() async {
    // Add listeners first
    _beneficiaryController.addListener(_onBeneficiaryChanged);
    _accountNumberController.addListener(_checkFieldsCleared);
    _bankController.addListener(_checkFieldsCleared);

    // Load async data first
    await loadCurrencies();
    await _loadUserData();

    // Populate form fields if editing
    if (widget.editItem != null) {
      _titleController.text = widget.editItem!.title;
      _beneficiaryController.text = widget.editItem!.accountHolder;
      _accountNumberController.text = widget.editItem!.bankNumber;
      _bankController.text = widget.editItem!.bankName;
      _amountController.text = formatCurrencyVND(widget.editItem!.price);
      _descriptionController.text = widget.editItem!.description;

      // Set currency for edit item
      if (widget.editItem!.currency.isNotEmpty) {
        selectedCurrency = currencies.firstWhere(
          (c) => c.code == widget.editItem!.currency,
          orElse: () => currencies.firstWhere((c) => c.code == 'VND'),
        );
      }

      // Set showSwiftCode based on currency
      showSwiftCode = widget.editItem!.currency != 'VND';
      if (showSwiftCode && widget.editItem!.swiftCode != null) {
        _swiftCodeController.text = widget.editItem!.swiftCode!;
      }

      // Handle attachments from edit item
      if (widget.editItem!.attachments != null) {
        _selectedFiles = widget.editItem!.attachments!.map((attachment) {
          return PlatformFile(
            name: attachment.fileName,
            size: int.tryParse(attachment.fileSize) ?? 0,
            bytes: null,
            path: attachment.url,
          );
        }).toList();
      }
    }

    // Save initial values after all data is loaded but before adding listeners
    _saveInitialValues();

    // Add change listeners after initial values are saved
    _beneficiaryController.addListener(_onFieldChanged);
    _accountNumberController.addListener(_onFieldChanged);
    _bankController.addListener(_onFieldChanged);
    _swiftCodeController.addListener(_onFieldChanged);
    _amountController.addListener(_onFieldChanged);
    _descriptionController.addListener(_onFieldChanged);
    _titleController.addListener(_onFieldChanged);
  }

  // Tải dữ liệu người dùng từ SharedPreferences để tự động điền vào form
  Future<void> _loadUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userDataString = prefs.getString('user_data');

      if (userDataString != null) {
        final userData = jsonDecode(userDataString);
        setState(() {
          // Không tự động điền thông tin người thụ hưởng nữa
          // Chỉ áp dụng điền thông tin khác nếu cần

          // Ví dụ: Có thể điền các trường không liên quan đến ngân hàng
          // _otherController.text = userData['other_field'] ?? '';
        });
      }
    } catch (e) {
      print('Error loading user data: $e');
      // Hiển thị thông báo lỗi nếu cần
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(
                  '${AppLocalizations.of(context).cannotLoadUserData}: $e')),
        );
      }
    }
  }

  // Tải danh sách các loại tiền tệ từ assets/data/currencies.json
  Future<void> loadCurrencies() async {
    try {
      final String jsonString =
          await rootBundle.loadString('assets/data/currencies.json');
      final List<dynamic> jsonList = json.decode(jsonString);

      setState(() {
        currencies = jsonList.map((json) => Currency.fromJson(json)).toList();
        // Đặt mặc định loại tiền là VND
        selectedCurrency = currencies.firstWhere(
          (c) => c.code == 'VND',
          orElse: () =>
              currencies.first, // Nếu không tìm thấy VND, chọn loại đầu tiên
        );
        isLoading = false;
      });
    } catch (e) {
      print('Error loading currencies: $e');
      setState(() {
        isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(
                  '${AppLocalizations.of(context).cannotLoadCurrencyData}: $e')),
        );
      }
    }
  }

  // Chọn file từ thiết bị
  Future<void> pickFiles() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['jpg', 'pdf', 'doc', 'docx', 'png'],
        allowMultiple: true,
        withData: true, // Lấy bytes của file, cần cho việc upload
      );

      if (result != null) {
        setState(() {
          // Thêm các file mới vào danh sách hiện có
          _selectedFiles = [
            ..._selectedFiles, // Giữ lại các file cũ
            ...result.files, // Thêm các file mới chọn
          ];
        });
        // Check for changes after adding files
        _onFieldChanged();
      }
    } catch (e) {
      print('Error picking files: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).cannotSelectFile),
          ),
        );
      }
    }
  }

  // Widget hiển thị xem trước file
  Widget _buildFilePreview(PlatformFile file) {
    // Nếu file có URL (tức là file cũ từ server)
    if (file.path?.startsWith('http') ?? false) {
      final extension = file.name.split('.').last.toLowerCase();
      if (['jpg', 'jpeg', 'png'].contains(extension)) {
        return Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            image: DecorationImage(
              image: NetworkImage(file.path!),
              fit: BoxFit.cover,
            ),
          ),
        );
      }
    }
    // Nếu là file mới được chọn (có bytes)
    else if (file.bytes != null) {
      final extension = file.name.split('.').last.toLowerCase();
      if (['jpg', 'jpeg', 'png'].contains(extension)) {
        return Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            image: DecorationImage(
              image: MemoryImage(file.bytes!),
              fit: BoxFit.cover,
            ),
          ),
        );
      }
    }

    // Hiển thị icon mặc định cho các loại file khác hoặc khi không có preview
    return Icon(
      file.name.endsWith('.pdf')
          ? Icons.picture_as_pdf
          : file.name.endsWith('.doc') || file.name.endsWith('.docx')
              ? Icons.description
              : Icons.insert_drive_file,
      color: AppColors.primary,
      size: 40,
    );
  }

  @override
  void dispose() {
    _beneficiaryController.removeListener(_onBeneficiaryChanged);
    _accountNumberController.removeListener(_checkFieldsCleared);
    _bankController.removeListener(_checkFieldsCleared);

    // Xóa các listener theo dõi thay đổi
    _beneficiaryController.removeListener(_onFieldChanged);
    _accountNumberController.removeListener(_onFieldChanged);
    _bankController.removeListener(_onFieldChanged);
    _swiftCodeController.removeListener(_onFieldChanged);
    _amountController.removeListener(_onFieldChanged);
    _descriptionController.removeListener(_onFieldChanged);
    _titleController.removeListener(_onFieldChanged);

    _beneficiaryController.dispose();
    _accountNumberController.dispose();
    _bankController.dispose();
    _swiftCodeController.dispose();
    _amountController.dispose();
    _descriptionController.dispose();
    _titleController.dispose();
    _beneficiaryFocusNode.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  // Listener khi người dùng nhập vào trường tên người thụ hưởng
  void _onBeneficiaryChanged() {
    // Kiểm tra ngay lập tức nếu đã chọn gợi ý, không xử lý gì thêm
    if (_suggestionSelected) {
      return;
    }

    final searchText = _beneficiaryController.text.trim();

    // Hủy debounce hiện tại nếu có
    if (_debounce?.isActive ?? false) {
      _debounce!.cancel();
    }

    // Clear suggestions if text is cleared
    if (searchText.isEmpty) {
      setState(() {
        _bankSuggestions = [];
      });
      return;
    }

    // Kiểm tra trạng thái các trường đã được điền
    final bankFilled = _bankController.text.trim().isNotEmpty;
    final accountFilled = _accountNumberController.text.trim().isNotEmpty;

    // Nếu đã điền đầy đủ thông tin ngân hàng, không gọi API nữa
    if (bankFilled && accountFilled) {
      // Luôn ẩn gợi ý trong trường hợp này
      setState(() {
        _bankSuggestions = [];
      });
      return;
    }

    // Debounce 500ms để tránh gọi API liên tục
    _debounce = Timer(const Duration(milliseconds: 500), () {
      // Kiểm tra lại một lần nữa để đảm bảo không gọi API nếu đã chọn gợi ý
      if (_suggestionSelected) {
        return;
      }

      // Chỉ gọi API khi có ít nhất 1 ký tự
      if (searchText.length >= 1) {
        _searchBankSuggestions(searchText);
      } else {
        setState(() {
          _bankSuggestions = [];
        });
      }
    });
  }

  // Hàm gọi API tìm kiếm gợi ý ngân hàng
  Future<void> _searchBankSuggestions(String query) async {
    if (query.isEmpty) return;

    setState(() {
      _isSearching = true;
    });

    try {
      final dio = Dio();
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? '';

      if (token.isEmpty) {
        throw Exception('Invalid token');
      }

      final response = await dio.get(
        '${ApiConfig.baseUrl}${ApiEndpoints.searchBank}',
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
        queryParameters: {
          'accountHolder': query,
        },
      );

      print('Bank search response: ${response.data}');

      if (response.statusCode == 200 && response.data['success'] == true) {
        final List<dynamic> data = response.data['data'];

        setState(() {
          _bankSuggestions =
              data.map((item) => BankSuggestion.fromJson(item)).toList();
          _isSearching = false;
        });
      } else {
        setState(() {
          _bankSuggestions = [];
          _isSearching = false;
        });
      }
    } catch (e) {
      print('Error searching bank suggestions: $e');
      setState(() {
        _bankSuggestions = [];
        _isSearching = false;
      });
    }
  }

  // Hàm áp dụng gợi ý được chọn
  void _applyBankSuggestion(BankSuggestion suggestion) {
    // Hủy mọi debounce đang chờ
    if (_debounce?.isActive ?? false) {
      _debounce!.cancel();
    }

    // Thiết lập cờ ngay lập tức để ngăn bất kỳ API nào được gọi
    _suggestionSelected = true;

    setState(() {
      _beneficiaryController.text = suggestion.accountHolder;
      _accountNumberController.text = suggestion.bankNumber;
      _bankController.text = suggestion.bankName;
      _bankSuggestions = []; // Xóa danh sách gợi ý sau khi chọn
    });

    // Bỏ focus khỏi trường tên người thụ hưởng
    _beneficiaryFocusNode.unfocus();

    // Hiển thị thông báo xác nhận
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
            '${AppLocalizations.of(context).appliedBankInfo}: ${suggestion.accountHolder}'),
        duration: const Duration(seconds: 2),
        backgroundColor: Colors.green,
      ),
    );
  }

  // Widget hiển thị danh sách gợi ý
  Widget _buildSuggestionsList() {
    if (_bankSuggestions.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(top: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      constraints: const BoxConstraints(maxHeight: 200),
      child: ListView.builder(
        shrinkWrap: true,
        itemCount: _bankSuggestions.length,
        itemBuilder: (context, index) {
          final suggestion = _bankSuggestions[index];
          return InkWell(
            onTap: () => _applyBankSuggestion(suggestion),
            child: Padding(
              padding:
                  const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    suggestion.accountHolder,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${suggestion.bankName} - ${suggestion.bankNumber}',
                    style: TextStyle(color: Colors.grey[700], fontSize: 13),
                  ),
                  if (index < _bankSuggestions.length - 1)
                    Divider(height: 16, color: Colors.grey[300]),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  // Xây dựng trường tên người thụ hưởng với chức năng gợi ý
  Widget _buildBeneficiaryField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _beneficiaryController,
          focusNode: _beneficiaryFocusNode,
          decoration: InputDecoration(
            labelText: AppLocalizations.of(context).beneficiaryName,
            hintText: AppLocalizations.of(context).enterBeneficiaryName,
            prefixIcon: const Icon(Icons.person),
            suffixIcon: _isSearching
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: Padding(
                      padding: EdgeInsets.all(12.0),
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                  )
                : const Icon(Icons.search),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            helperText: AppLocalizations.of(context).searchBankInfoHelper,
            floatingLabelBehavior: FloatingLabelBehavior.always,
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return AppLocalizations.of(context).pleaseEnterBeneficiaryName;
            }
            return null;
          },
          textInputAction: TextInputAction.search,
          onChanged: (value) {
            // Đã có listener trong initState
          },
        ),
        _buildSuggestionsList(),
      ],
    );
  }

  // Hàm kiểm tra nếu các trường bị xóa thì reset cờ đã chọn gợi ý
  void _checkFieldsCleared() {
    // Chỉ reset cờ khi các trường quan trọng bị xóa HOÀN TOÀN
    if (_suggestionSelected) {
      // Nếu một trong các trường quan trọng bị xóa hoàn toàn, reset cờ
      if (_accountNumberController.text.trim().isEmpty ||
          _bankController.text.trim().isEmpty) {
        setState(() {
          _suggestionSelected = false;
        });
      }
    }
  }

  // Hàm kiểm tra nếu có sự thay đổi dữ liệu
  void _onFieldChanged() {
    // Check if files have changed by comparing current count with initial count
    final filesChanged = _selectedFiles.length != _initialFileCount;

    final hasChanges = _beneficiaryController.text != _initialBeneficiary ||
        _accountNumberController.text != _initialAccount ||
        _bankController.text != _initialBank ||
        _swiftCodeController.text != _initialSwiftCode ||
        _amountController.text != _initialAmount ||
        _descriptionController.text != _initialDescription ||
        _titleController.text != _initialTitle ||
        selectedCurrency != _initialCurrency ||
        filesChanged; // Check if file count has changed from initial

    setState(() {
      _hasChanges = hasChanges;
    });
  }

  // Hàm lưu giá trị ban đầu của các trường
  void _saveInitialValues() {
    _initialBeneficiary = _beneficiaryController.text;
    _initialAccount = _accountNumberController.text;
    _initialBank = _bankController.text;
    _initialSwiftCode = _swiftCodeController.text;
    _initialAmount = _amountController.text;
    _initialDescription = _descriptionController.text;
    _initialTitle = _titleController.text;
    _initialCurrency = selectedCurrency;
    _initialFileCount = _selectedFiles.length; // Save initial file count
  }

  // Hàm gửi form data lên server
  // Hàm gửi form data lên server - Đã sửa lỗi
  Future<void> _uploadFormData({required String status}) async {
    // Simply return if validation fails
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Store localized strings before async operations
    final localizations = AppLocalizations.of(context);
    final connectionTimeoutMsg = localizations.connectionTimeout;
    final noServerResponseMsg = localizations.noServerResponse;
    final serverErrorMsg = localizations.serverError;
    final requestCancelledMsg = localizations.requestCancelled;
    final cannotConnectServerMsg = localizations.cannotConnectServer;
    final unknownErrorMsg = localizations.unknownError;
    final generalErrorMsg = localizations.generalError;

    setState(() => _isSubmitting = true);

    try {
      final dio = Dio();

      // Thêm timeout để tránh app đơ
      dio.options.connectTimeout = const Duration(seconds: 30);
      dio.options.receiveTimeout = const Duration(seconds: 30);

      final formData = FormData();

      // Add form fields
      if (widget.editItem != null) {
        formData.fields.addAll([
          MapEntry('title', _titleController.text),
          MapEntry('categoryId', '1'),
          MapEntry('currency', selectedCurrency?.code ?? 'VND'),
          MapEntry('bankNumber', _accountNumberController.text), // Thêm vào
          MapEntry('accountHolder', _beneficiaryController.text), // Thêm vào
          MapEntry('bankName', _bankController.text), // Thêm vào
          if (showSwiftCode && _swiftCodeController.text.isNotEmpty)
            MapEntry('swiftCode', _swiftCodeController.text),
          MapEntry('price', _amountController.text.replaceAll('.', '')),
          MapEntry('description', _descriptionController.text),
          MapEntry('buyerName', _beneficiaryController.text), // Thêm vào
          MapEntry('status', status),
          MapEntry('taxTypeId', '1'),
        ]);
      } else {
        formData.fields.addAll([
          MapEntry('title', _titleController.text),
          MapEntry('categoryId', '1'),
          MapEntry('bankNumber', _accountNumberController.text),
          MapEntry('accountHolder', _beneficiaryController.text),
          MapEntry('bankName', _bankController.text),
          MapEntry('currency', selectedCurrency?.code ?? 'VND'),
          MapEntry('price', _amountController.text.replaceAll('.', '')),
          if (showSwiftCode && _swiftCodeController.text.isNotEmpty)
            MapEntry('swiftCode', _swiftCodeController.text),
          MapEntry('description', _descriptionController.text),
          MapEntry('buyerName', _beneficiaryController.text),
          MapEntry('status', status), // Use passed status
          MapEntry('taxTypeId', '1'),
        ]);
      }

      // Add files với xử lý lỗi tốt hơn
      for (var file in _selectedFiles) {
        try {
          if (file.bytes != null) {
            formData.files.add(
              MapEntry(
                'attachments[]',
                MultipartFile.fromBytes(file.bytes!, filename: file.name),
              ),
            );
          } else if (file.path != null && !file.path!.startsWith('http')) {
            formData.files.add(
              MapEntry(
                'attachments[]',
                await MultipartFile.fromFile(file.path!, filename: file.name),
              ),
            );
          }
        } catch (fileError) {
          print('Error processing file ${file.name}: $fileError');
          // Tiếp tục với các file khác thay vì dừng toàn bộ
        }
      }

      // Get auth token
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? '';

      if (token.isEmpty) {
        throw Exception('Invalid token. Please login again.');
      }

      // Configure Dio
      dio.options.headers = {
        'Authorization': 'Bearer $token',
        'Content-Type': 'multipart/form-data',
      };

      final String url = widget.editItem != null
          ? '${ApiConfig.baseUrl}${ApiEndpoints.upDateForm}/${widget.editItem!.id}'
          : '${ApiConfig.baseUrl}${ApiEndpoints.upLoadForm}';

      print('=== API REQUEST DEBUG ===');
      print('URL: $url');

      final response = await dio.post(
        url,
        data: formData,
        onSendProgress: (sent, total) {
          if (total != -1) {
            print(
                'Upload Progress: ${(sent / total * 100).toStringAsFixed(0)}%');
          }
        },
      );

      print('Response Status: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        int formId = -1;
        if (response.data != null &&
            response.data['success'] == true &&
            response.data['data'] != null &&
            response.data['data']['id'] != null) {
          formId = response.data['data']['id'];

          if (status == 'pendingaccountant') {
            try {
              final notificationDio = Dio();
              final prefs = await SharedPreferences.getInstance();
              final token = prefs.getString('auth_token') ?? '';

              notificationDio.options.headers = {
                'Authorization': 'Bearer $token',
                'Content-Type': 'application/json',
              };

              // 发送通知给会计的API请求
              final notificationResponse = await notificationDio.post(
                '${ApiConfig.baseUrl}${ApiEndpoints.sendNotificationToAccountant}',
                data: {'form_id': formId, 'action': 'submitted_by_submitter'},
              );

              print(
                  'Notification to accountant sent successfully: ${notificationResponse.statusCode}');
            } catch (notificationError) {
              // 仅记录错误，不影响主流程
              print(
                  'Error sending notification to accountant: $notificationError');
            }
          }
        }

        if (mounted) {
          // Hide keyboard
          FocusScope.of(context).unfocus();

          // Show success dialog
          showDialog(
            context: context,
            barrierDismissible: false, // User must tap OK
            builder: (BuildContext dialogContext) {
              return AlertDialog(
                title: Text(AppLocalizations.of(context).success),
                content: Text(widget.editItem != null
                    ? AppLocalizations.of(context).updateRequestSuccess
                    : AppLocalizations.of(context).createPaymentRequestSuccess),
                actions: [
                  TextButton(
                    onPressed: () {
                      // Call refresh callback
                      widget.onSubmitSuccess?.call();

                      // Navigate to HomePage and remove all previous routes
                      Navigator.pushAndRemoveUntil(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const HomePage()),
                        (route) => false,
                      );
                    },
                    child: const Text('OK'),
                  ),
                ],
              );
            },
          );
        }
      } else {
        throw DioException(
          requestOptions: RequestOptions(path: url),
          response: response,
          message: 'API error: ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      print('=== DIO ERROR ===');
      print('Error Type: ${e.type}');
      print('Error Message: ${e.message}');

      String errorMessage;
      switch (e.type) {
        case DioExceptionType.connectionTimeout:
          errorMessage = connectionTimeoutMsg;
          break;
        case DioExceptionType.receiveTimeout:
          errorMessage = noServerResponseMsg;
          break;
        case DioExceptionType.badResponse:
          errorMessage = '$serverErrorMsg: ${e.response?.statusCode}';
          break;
        case DioExceptionType.cancel:
          errorMessage = requestCancelledMsg;
          break;
        case DioExceptionType.connectionError:
          errorMessage = cannotConnectServerMsg;
          break;
        default:
          errorMessage = '$unknownErrorMsg: ${e.message}';
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      print('General Error: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('$generalErrorMsg: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      // Đảm bảo state được reset trong mọi trường hợp
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  // Add new method for handling Send button
  Future<void> _handleSend() async {
    await _uploadFormData(status: 'pendingaccountant');
  }

  // Add new method for handling Save Draft button
  Future<void> _handleSaveDraft() async {
    await _uploadFormData(status: 'draft');
  }

  // Hàm hiển thị dialog xác nhận khi người dùng muốn quay lại
  Future<bool> _showConfirmationDialog() async {
    if (!_hasChanges)
      return true; // Nếu không có thay đổi, cho phép quay lại ngay

    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text(AppLocalizations.of(context).confirm),
          content: Text(AppLocalizations.of(context).unsavedChangesConfirm),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(dialogContext, false); // Không, ở lại
              },
              child: Text(AppLocalizations.of(context).no),
            ),
            TextButton(
              onPressed: () {
                Navigator.pushAndRemoveUntil(
                  context,
                  MaterialPageRoute(builder: (context) => HomePage()),
                  (Route<dynamic> route) => false,
                );
              },
              child: Text(AppLocalizations.of(context).yes),
            ),
          ],
        );
      },
    );

    return result ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: !_hasChanges && !_isSubmitting,
      onPopInvoked: (didPop) async {
        // If already popped, don't do anything
        if (didPop) {
          return;
        }

        // If submitting, show message and prevent pop
        if (_isSubmitting) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context).processingWait),
              duration: Duration(seconds: 2),
            ),
          );
          return;
        }

        // If has changes, show confirmation dialog
        if (_hasChanges) {
          final shouldPop = await _showConfirmationDialog();
          if (shouldPop) {
            // Cancel any running processes
            if (_debounce?.isActive ?? false) {
              _debounce!.cancel();
            }

            // Pop with result indicating changes were made
            if (context.mounted) {
              Navigator.of(context).pop(true);
            }
          }
        }
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title:
              Text(widget.title, style: const TextStyle(color: Colors.black)),
          backgroundColor: Colors.white,
          elevation: 0,
          iconTheme: const IconThemeData(color: Colors.black),
          scrolledUnderElevation: 0,
          surfaceTintColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () async {
              // Hủy mọi tiến trình đang chạy
              if (_debounce?.isActive ?? false) {
                _debounce!.cancel();
              }

              // Nếu đang submit, hiển thị thông báo
              if (_isSubmitting) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(AppLocalizations.of(context).processingWait),
                    duration: Duration(seconds: 2),
                  ),
                );
                return;
              }

              // Kiểm tra xác nhận nếu có thay đổi
              if (_hasChanges) {
                final shouldPop = await _showConfirmationDialog();
                if (!shouldPop) return; // Nếu người dùng chọn ở lại, dừng xử lý

                // Return true to indicate changes were made but user chose to exit
                if (context.mounted) {
                  Navigator.of(context).pop(true);
                }
              } else {
                // No changes made, exit cleanly
                if (context.mounted) {
                  Navigator.of(context).pop(false);
                }
              }
            },
          ),
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(1),
            child: Container(
              color: Colors.grey.shade300,
              height: 1,
            ),
          ),
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocalizations.of(context).enterInformation,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),
                // Tiêu đề thanh toán
                TextFormField(
                  controller: _titleController,
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context).paymentTitle,
                    border: OutlineInputBorder(),
                    prefixIcon:
                        Icon(Icons.description_outlined), // Thay đổi icon
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppLocalizations.of(context)
                          .pleaseEnterPaymentTitle;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                // Tên người thụ hưởng (chỉ đọc)
                _buildBeneficiaryField(),
                const SizedBox(height: 16),
                // Số tài khoản
                TextFormField(
                  controller: _accountNumberController,
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context).accountNumber,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    prefixIcon: const Icon(Icons.credit_card),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppLocalizations.of(context)
                          .pleaseEnterAccountNumber;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                // Ngân hàng
                TextFormField(
                  controller: _bankController,
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context).bank,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    prefixIcon: const Icon(Icons.account_balance),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppLocalizations.of(context).pleaseEnterBankName;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                // Dropdown chọn loại tiền tệ
                DropdownButtonFormField<Currency>(
                  value: selectedCurrency,
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context).paymentCurrencyType,
                    border: OutlineInputBorder(),
                  ),
                  menuMaxHeight: 200,
                  items: currencies.map((Currency currency) {
                    return DropdownMenuItem<Currency>(
                      value: currency,
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children: [
                            Text(
                              currency.code,
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(width: 8),
                            Text(currency.currency),
                            const SizedBox(width: 8),
                            Text(
                              '(${currency.country})',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }).toList(),
                  onChanged: (Currency? value) {
                    setState(() {
                      selectedCurrency = value;
                      // Show SwiftCode field only for non-VND currencies
                      showSwiftCode = value?.code != 'VND';
                      // Clear SwiftCode when switching back to VND
                      if (!showSwiftCode) {
                        _swiftCodeController.clear();
                      }
                    });
                    // Check for changes after currency change
                    _onFieldChanged();
                  },
                  validator: (value) {
                    if (value == null) {
                      return AppLocalizations.of(context)
                          .pleaseSelectCurrencyType;
                    }
                    return null;
                  },
                ),
                // Swift Code (chỉ hiển thị nếu cần)
                if (showSwiftCode) ...[
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _swiftCodeController,
                    decoration: InputDecoration(
                      labelText: AppLocalizations.of(context).swiftCode,
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.numbers_outlined), // Thay đổi icon
                    ),
                    validator: (value) {
                      if (showSwiftCode && (value == null || value.isEmpty)) {
                        return AppLocalizations.of(context)
                            .pleaseEnterSwiftCode;
                      }
                      return null;
                    },
                  ),
                ],
                const SizedBox(height: 16),
                // Số tiền
                TextFormField(
                  controller: _amountController,
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context).amount,
                    border: const OutlineInputBorder(),
                    prefixIcon: Container(
                      width: 50,
                      alignment: Alignment.center,
                      child: Text(
                        selectedCurrency?.symbol ?? '₫', // Ký hiệu tiền tệ động
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    ThousandsSeparatorInputFormatter(),
                  ],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppLocalizations.of(context).pleaseEnterAmount;
                    }
                    final numericValue = value.replaceAll('.', '');
                    if (int.tryParse(numericValue) == null) {
                      return AppLocalizations.of(context)
                          .pleaseEnterValidNumber;
                    }
                    return null;
                  },
                  onChanged: (value) {
                    // Có thể thêm logic xử lý khi giá trị thay đổi ở đây
                  },
                ),
                const SizedBox(height: 16),
                // Nội dung thanh toán (previously Diễn giải)
                TextFormField(
                  controller: _descriptionController,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(),
                    labelText: AppLocalizations.of(context).paymentContent,
                    prefixIcon: Icon(Icons.edit_note_outlined),
                  ),
                  maxLines: 3,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Vui lòng nhập nội dung thanh toán'; // Updated validation message
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                // Khu vực đính kèm tài liệu
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ElevatedButton.icon(
                      onPressed: pickFiles,
                      icon: const Icon(
                        Icons.attach_file,
                        color: Colors.white,
                      ),
                      label: const Text('Đính kèm tài liệu'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    if (_selectedFiles.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      const Text(
                        'Tệp đã chọn:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      // Danh sách các file đã chọn
                      ..._selectedFiles
                          .map((file) => Padding(
                                padding: const EdgeInsets.only(bottom: 8),
                                child: ListTile(
                                  contentPadding: EdgeInsets.zero,
                                  leading: _buildFilePreview(file),
                                  title: Text(file.name),
                                  subtitle: Text(
                                      '${(file.size / 1024).toStringAsFixed(2)} KB'),
                                  trailing: IconButton(
                                    icon: const Icon(Icons.close,
                                        color: Colors.red),
                                    onPressed: () {
                                      setState(() {
                                        _selectedFiles.remove(file);
                                      });
                                      // Check for changes after removing file
                                      _onFieldChanged();
                                    },
                                  ),
                                ),
                              ))
                          .toList(),
                    ],
                  ],
                ),
                const SizedBox(height: 24),
                // Replace single button with two buttons in a row
                Row(
                  children: [
                    // Draft button
                    Expanded(
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey[300],
                          foregroundColor: Colors.black87,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        onPressed: _isSubmitting
                            ? null
                            : () {
                                // Set status to 'draft' before submitting
                                _handleSaveDraft();
                              },
                        child: _isSubmitting
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.black54),
                                ),
                              )
                            : const Text(
                                'Save Draft',
                                style: TextStyle(fontSize: 16),
                              ),
                      ),
                    ),
                    const SizedBox(width: 16), // Spacing between buttons
                    // Send button
                    Expanded(
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        onPressed: _isSubmitting
                            ? null
                            : () {
                                // Set status to 'pendingaccountant' before submitting
                                _handleSend();
                              },
                        child: _isSubmitting
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white),
                                ),
                              )
                            : const Text(
                                'Send',
                                style: TextStyle(fontSize: 16),
                              ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
