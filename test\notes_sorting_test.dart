import 'package:flutter_test/flutter_test.dart';
import 'package:du_an_flutter/model/note.dart';

void main() {
  group('Notes Sorting Logic Test', () {
    test('should sort notes with today first, then chronological order', () {
      // Get today's date in ISO format
      final today = DateTime.now();
      final tomorrow = today.add(const Duration(days: 1));
      final dayAfterTomorrow = today.add(const Duration(days: 2));
      final yesterday = today.subtract(const Duration(days: 1));
      
      // Create test notes with different dates
      final notes = [
        Note(
          id: 1,
          title: 'Tomorrow Note',
          description: 'This is tomorrow',
          notiDate: tomorrow.toIso8601String(),
          isRead: false,
        ),
        Note(
          id: 2,
          title: 'Today Note 1',
          description: 'This is today',
          notiDate: today.toIso8601String(),
          isRead: false,
        ),
        Note(
          id: 3,
          title: 'Day After Tomorrow Note',
          description: 'This is day after tomorrow',
          notiDate: dayAfterTomorrow.toIso8601String(),
          isRead: false,
        ),
        Note(
          id: 4,
          title: 'Today Note 2',
          description: 'This is also today',
          notiDate: today.toIso8601String(),
          isRead: false,
        ),
        Note(
          id: 5,
          title: 'Yesterday Note',
          description: 'This is yesterday',
          notiDate: yesterday.toIso8601String(),
          isRead: false,
        ),
      ];

      // Apply the same sorting logic as in NotesService.getUnreadNotes()
      notes.sort((a, b) {
        try {
          final dateA = DateTime.parse(a.notiDate);
          final dateB = DateTime.parse(b.notiDate);
          
          // Get today's date for comparison
          final todayFormatted = _formatDateForAPI(today);
          
          // Check if notes are due today
          final isAToday = _formatDateForAPI(dateA) == todayFormatted;
          final isBToday = _formatDateForAPI(dateB) == todayFormatted;
          
          // Priority 1: Today's notes always come first
          if (isAToday && !isBToday) {
            return -1; // A (today) comes before B
          } else if (!isAToday && isBToday) {
            return 1; // B (today) comes before A
          } else if (isAToday && isBToday) {
            // Both are today's notes, sort by time if available, or keep original order
            return 0;
          } else {
            // Priority 2: For non-today notes, sort chronologically (nearest dates first)
            return dateA.compareTo(dateB); // Ascending order: tomorrow, day after, etc.
          }
        } catch (e) {
          return 0; // Keep original order if date parsing fails
        }
      });

      // Verify the sorting order
      // Today's notes should come first (id: 2, 4)
      expect(notes[0].id, anyOf([2, 4])); // Today Note 1 or Today Note 2
      expect(notes[1].id, anyOf([2, 4])); // Today Note 1 or Today Note 2
      
      // Then chronological order: yesterday, tomorrow, day after tomorrow
      expect(notes[2].id, 5); // Yesterday Note
      expect(notes[3].id, 1); // Tomorrow Note  
      expect(notes[4].id, 3); // Day After Tomorrow Note
      
      print('Sorted notes order:');
      for (int i = 0; i < notes.length; i++) {
        print('${i + 1}. ${notes[i].title} (ID: ${notes[i].id})');
      }
    });
  });
}

// Helper method to format date for API (same as in NotesService)
String _formatDateForAPI(DateTime date) {
  return '${date.year.toString().padLeft(4, '0')}-'
      '${date.month.toString().padLeft(2, '0')}-'
      '${date.day.toString().padLeft(2, '0')}';
}
