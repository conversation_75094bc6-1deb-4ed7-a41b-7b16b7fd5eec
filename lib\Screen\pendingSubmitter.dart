import 'package:du_an_flutter/Screen/listActivities.dart';
import 'package:du_an_flutter/model/activity_item.dart';
import 'package:du_an_flutter/services/api_service.dart';
import 'package:du_an_flutter/widgets/loading_overlay.dart';
import 'package:flutter/material.dart';

// Export the state class
typedef PendingSubmitterScreenState = _PendingSubmitterScreenState;

// Global key for external refresh access
final GlobalKey<PendingSubmitterScreenState> pendingSubmitterKey =
    GlobalKey<PendingSubmitterScreenState>();

class PendingSubmitterScreen extends StatefulWidget {
  const PendingSubmitterScreen({super.key});

  @override
  State<PendingSubmitterScreen> createState() => _PendingSubmitterScreenState();
}

class _PendingSubmitterScreenState extends State<PendingSubmitterScreen> {
  List<ActivityItem> items = [];

  // Add loading state
  bool get isLoading => _isLoading;
  bool _isLoading = true;
  DateTime? _lastLoadTime;

  // Static instance for external refresh triggers
  static _PendingSubmitterScreenState? _instance;

  // Static method to trigger refresh from external sources (like status changes)
  static Future<void> triggerRefresh() async {
    print(
        'PendingSubmitterScreen - triggerRefresh called from external source');
    if (_instance != null && _instance!.mounted) {
      print('PendingSubmitterScreen - Instance available, triggering refresh');
      _instance!.forceReload();
    } else {
      print('PendingSubmitterScreen - Instance not available or not mounted');
    }
  }

  final List<Map<String, dynamic>> statusItems = [
    {
      'title': 'Draft',
      'count': 0,
      'color': Colors.grey,
    },
    {
      'title': 'Cancel',
      'count': 0,
      'color': Colors.grey,
    },
    {
      'title': 'Fail',
      'count': 0,
      'color': Colors.red,
    },
    {
      'title': 'PendingAccountant',
      'count': 0,
      'color': Colors.amber,
    },
    {
      'title': 'PendingApprover',
      'count': 0,
      'color': Colors.lightBlue,
    },
    {
      'title': 'Approved',
      'count': 0,
      'color': Colors.lightGreen,
    },
  ];

  @override
  void initState() {
    super.initState();
    // Set static instance for external access
    _instance = this;
    print("PendingSubmitterScreen - initState called");
    _loadActivities();
    _updateStatusCounts();
  }

  @override
  void dispose() {
    // Clear static instance
    if (_instance == this) {
      _instance = null;
    }
    super.dispose();
    print("PendingSubmitterScreen - dispose called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("PendingSubmitterScreen - didChangeDependencies called");
    _loadActivities();
  }

  @override
  void activate() {
    super.activate();
    print("PendingSubmitterScreen - activate called - RELOADING API DATA");
    // Force reload when tab is activated
    _loadActivities();
  }

  // Force reload method for manual refresh
  void forceReload() {
    print("PendingSubmitterScreen - forceReload called");
    _lastLoadTime = null;
    _loadActivities();
  }

  // Public method for external refresh calls (used by GlobalKey)
  void refreshData() {
    print("PendingSubmitterScreen - refreshData called externally");
    forceReload();
  }

  // Update status counts
  void _updateStatusCounts() {
    if (items.isNotEmpty) {
      // Count activities by status
      for (var item in statusItems) {
        final String statusTitle = item['title'];
        final count = items
            .where((activity) =>
                activity.status.toLowerCase() == statusTitle.toLowerCase())
            .length;
        item['count'] = count;
      }
    }
  }

  Future<void> _loadActivities() async {
    print("PendingSubmitterScreen - _loadActivities started");
    if (!mounted) {
      print("PendingSubmitterScreen - not mounted, aborting _loadActivities");
      return;
    }

    setState(() => _isLoading = true);
    print("PendingSubmitterScreen - set loading state to true");

    try {
      print("PendingSubmitterScreen - calling API.getForm()");
      final activities = await ApiService.getForm();
      print(
          "PendingSubmitterScreen - API.getForm() completed with ${activities.length} items");

      if (!mounted) {
        print("PendingSubmitterScreen - not mounted after API call, aborting");
        return;
      }

      setState(() {
        items = activities;
        _isLoading = false;
        _lastLoadTime = DateTime.now();
        print(
            "PendingSubmitterScreen - updated state with new data, timestamp: ${_lastLoadTime}");
      });

      // Update counts after getting new data
      _updateStatusCounts();
    } catch (e) {
      print('PendingSubmitterScreen - Error loading activities: $e');
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: LoadingOverlay(),
      );
    }

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          'Trạng thái phê duyệt',
          style: TextStyle(color: Colors.black),
        ),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.black),
        // Add manual refresh button
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: forceReload,
          ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1.0),
          child: Container(
            color: Colors.grey.shade600,
            height: 1.0,
          ),
        ),
      ),
      body: RefreshIndicator(
        onRefresh: _loadActivities,
        child: ListView.separated(
          itemCount: statusItems.length,
          separatorBuilder: (_, __) => const Divider(height: 0),
          itemBuilder: (context, index) {
            return ListTile(
              title: Text(
                statusItems[index]['title']!,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: statusItems[index]['color'],
                      borderRadius: BorderRadius.circular(5),
                    ),
                    child: Text(
                      '${statusItems[index]['count']}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 10),
                  const Icon(Icons.chevron_right),
                ],
              ),
              onTap: () async {
                final filteredActivities = items
                    .where((item) =>
                        item.status.toLowerCase() ==
                        statusItems[index]['title'].toLowerCase())
                    .toList();

                // Update immediately when returning from the next screen
                await Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => Listactivities(
                      status: statusItems[index]['title'],
                      activities: filteredActivities,
                      onRefresh: () async {
                        // Callback to refresh parent screen data
                        await _loadActivities();
                      },
                    ),
                  ),
                );

                // Reload data when coming back
                _loadActivities();
              },
            );
          },
        ),
      ),
    );
  }
}
