import 'package:flutter/material.dart';
import 'package:du_an_flutter/model/activity_item.dart';
import 'package:du_an_flutter/Screen/activities_screen.dart';
import 'package:du_an_flutter/l10n/app_localizations.dart';
import 'dart:async';

class Listactivities extends StatefulWidget {
  final String status;
  final List<ActivityItem> activities;
  final Function? onRefresh;

  const Listactivities({
    Key? key,
    required this.status,
    required this.activities,
    this.onRefresh,
  }) : super(key: key);

  @override
  State<Listactivities> createState() => _ListactivitiesState();
}

class _ListactivitiesState extends State<Listactivities> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  Timer? _debounce;
  List<ActivityItem> filteredActivities = [];

  @override
  void initState() {
    super.initState();
    filteredActivities = widget.activities;
  }

  @override
  void dispose() {
    // Cancel any active timer to prevent setState after dispose
    _debounce?.cancel();
    _debounce = null;
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  /// Normalizes Vietnamese text by removing diacritical marks for search comparison
  /// Example: "hóa đơn" becomes "hoa don" to match search query "hoa"
  static String _normalizeVietnameseText(String text) {
    const Map<String, String> diacriticMap = {
      // Lowercase vowels with diacritics
      'à': 'a', 'á': 'a', 'ạ': 'a', 'ả': 'a', 'ã': 'a',
      'â': 'a', 'ầ': 'a', 'ấ': 'a', 'ậ': 'a', 'ẩ': 'a', 'ẫ': 'a',
      'ă': 'a', 'ằ': 'a', 'ắ': 'a', 'ặ': 'a', 'ẳ': 'a', 'ẵ': 'a',
      'è': 'e', 'é': 'e', 'ẹ': 'e', 'ẻ': 'e', 'ẽ': 'e',
      'ê': 'e', 'ề': 'e', 'ế': 'e', 'ệ': 'e', 'ể': 'e', 'ễ': 'e',
      'ì': 'i', 'í': 'i', 'ị': 'i', 'ỉ': 'i', 'ĩ': 'i',
      'ò': 'o', 'ó': 'o', 'ọ': 'o', 'ỏ': 'o', 'õ': 'o',
      'ô': 'o', 'ồ': 'o', 'ố': 'o', 'ộ': 'o', 'ổ': 'o', 'ỗ': 'o',
      'ơ': 'o', 'ờ': 'o', 'ớ': 'o', 'ợ': 'o', 'ở': 'o', 'ỡ': 'o',
      'ù': 'u', 'ú': 'u', 'ụ': 'u', 'ủ': 'u', 'ũ': 'u',
      'ư': 'u', 'ừ': 'u', 'ứ': 'u', 'ự': 'u', 'ử': 'u', 'ữ': 'u',
      'ỳ': 'y', 'ý': 'y', 'ỵ': 'y', 'ỷ': 'y', 'ỹ': 'y',
      'đ': 'd',
      // Uppercase vowels with diacritics
      'À': 'A', 'Á': 'A', 'Ạ': 'A', 'Ả': 'A', 'Ã': 'A',
      'Â': 'A', 'Ầ': 'A', 'Ấ': 'A', 'Ậ': 'A', 'Ẩ': 'A', 'Ẫ': 'A',
      'Ă': 'A', 'Ằ': 'A', 'Ắ': 'A', 'Ặ': 'A', 'Ẳ': 'A', 'Ẵ': 'A',
      'È': 'E', 'É': 'E', 'Ẹ': 'E', 'Ẻ': 'E', 'Ẽ': 'E',
      'Ê': 'E', 'Ề': 'E', 'Ế': 'E', 'Ệ': 'E', 'Ể': 'E', 'Ễ': 'E',
      'Ì': 'I', 'Í': 'I', 'Ị': 'I', 'Ỉ': 'I', 'Ĩ': 'I',
      'Ò': 'O', 'Ó': 'O', 'Ọ': 'O', 'Ỏ': 'O', 'Õ': 'O',
      'Ô': 'O', 'Ồ': 'O', 'Ố': 'O', 'Ộ': 'O', 'Ổ': 'O', 'Ỗ': 'O',
      'Ơ': 'O', 'Ờ': 'O', 'Ớ': 'O', 'Ợ': 'O', 'Ở': 'O', 'Ỡ': 'O',
      'Ù': 'U', 'Ú': 'U', 'Ụ': 'U', 'Ủ': 'U', 'Ũ': 'U',
      'Ư': 'U', 'Ừ': 'U', 'Ứ': 'U', 'Ự': 'U', 'Ử': 'U', 'Ữ': 'U',
      'Ỳ': 'Y', 'Ý': 'Y', 'Ỵ': 'Y', 'Ỷ': 'Y', 'Ỹ': 'Y',
      'Đ': 'D',
    };

    String normalized = text;
    diacriticMap.forEach((diacritic, base) {
      normalized = normalized.replaceAll(diacritic, base);
    });
    return normalized;
  }

  @override
  void activate() {
    super.activate();
    // Store context before async operation to prevent BuildContext async gap
    final localizations = AppLocalizations.of(context);
    print(localizations.listActivitiesReactivated);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        FocusManager.instance.primaryFocus?.unfocus();
        _searchFocusNode.unfocus();
      }
    });
  }

  void _onSearchChanged(String query) {
    // Cancel any existing timer
    if (_debounce?.isActive ?? false) _debounce?.cancel();

    // Only create new timer if widget is still mounted
    if (mounted) {
      _debounce = Timer(const Duration(milliseconds: 500), () {
        // Double-check if widget is still mounted before calling setState
        if (mounted && _debounce != null) {
          setState(() {
            filteredActivities = widget.activities.where((activity) {
              // Normalize both search query and activity title for diacritic-insensitive search
              final normalizedQuery =
                  _normalizeVietnameseText(query.toLowerCase());
              final normalizedTitle =
                  _normalizeVietnameseText(activity.title.toLowerCase());
              return normalizedTitle.contains(normalizedQuery);
            }).toList();
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        scrolledUnderElevation: 0,
        surfaceTintColor: Colors.white,
        title: Text(
          '${AppLocalizations.of(context).listTitle} ${widget.status} (${widget.activities.length})',
          style: TextStyle(color: Colors.black),
        ),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.black),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: TextField(
              controller: _searchController,
              focusNode: _searchFocusNode,
              decoration: InputDecoration(
                hintText: AppLocalizations.of(context).searchPlaceholder,
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16),
              ),
              onChanged: _onSearchChanged,
            ),
          ),
          Expanded(
            child: filteredActivities.isEmpty
                ? Center(
                    child: Text(
                      AppLocalizations.of(context).noDataAvailable,
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(8),
                    itemCount: filteredActivities.length,
                    itemBuilder: (context, index) {
                      return ActivityScreen(
                        item: filteredActivities[index],
                        onRefresh: () async {
                          // Refresh the parent's data and update the filtered list
                          await widget.onRefresh?.call();
                          // Check if widget is still mounted before triggering search
                          if (mounted) {
                            _onSearchChanged(_searchController.text);
                          }
                        },
                        onStatusChanged: () {
                          // Pop back to pendingSubmitter when status changes successfully
                          if (mounted && Navigator.canPop(context)) {
                            Navigator.pop(context);
                          }
                        },
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }
}
