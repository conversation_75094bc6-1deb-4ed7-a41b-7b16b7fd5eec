import 'package:flutter/material.dart';

class AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['en', 'vi'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(AppLocalizationsDelegate old) => false;
}

class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations) ??
        AppLocalizations(const Locale('vi'));
  }

  static final Map<String, Map<String, String>> _localizedValues = {
    'en': {
      'personalInfo': 'Personal Information',
      'fullName': 'Full Name',
      'email': 'Email',
      'phoneNumber': 'Phone Number',
      'companyname': 'Company Name',
      'saveChanges': 'Save Changes',
      'updateSuccess': 'Information updated successfully',
      'logout': 'Logout',
      'cancel': 'Cancel',
      'confirmLogout': 'Are you sure you want to logout?',
      'language': 'Language',
      'vietnamese': 'Vietnamese',
      'english': 'English',
      'amount': 'Amount',
      'status': 'Status',
      'category': 'Category',
      'attachments': 'Attachments',
      'confirmDelete': 'Confirm Delete',
      'delete': 'Delete',
      'edit': 'Edit',
      'searchHint': 'Search...',
      'list': 'List',
      'filterTitle': 'Filter',
      'selectDate': 'Select Date',
      'date': 'Date',
      'clearFilter': 'Clear Filter',
      'applyFilter': 'Apply',
      'filterTooltip': 'Filter list',
      'pending': 'Pending',
      'approved': 'Approved',
      'rejected': 'Rejected',
      'statusApproved': 'Approved',
      'statusPendingApprover': 'Pending Approval',
      'statusPendingAccountant': 'Pending Accountant',
      'statusFailed': 'Rejected',
      'statusDraft': 'Draft',
      'statusCancelled': 'Cancelled',
      'notification': 'Notification',
      'unreadNotesTitle': 'Unread Note Notifications',
      'notesCount': 'notes',
      'ok': 'OK',
      'details': 'Details',
      'selectFromGallery': 'Select from Gallery',
      'takeNewPhoto': 'Take New Photo',
      'selectBank': 'Select Bank',
      'errorLoadingProfile': 'Unable to load user information',
      'invalidEmail': 'Email must be in @gmail.com format',
      'pleaseEnterPassword': 'Please enter password',
      'pleaseEnterNewPassword': 'Please enter new password',
      'pleaseConfirmPassword': 'Please confirm new password',
      'passwordMismatch': 'Confirmation password does not match',
      'approveSuccess': 'Request approved successfully',
      'rejectSuccess': 'Request rejected successfully',
      'pleaseEnterReason': 'Please enter rejection reason',
      'invalidOTP': 'OTP code is incorrect!',
      'pleaseCompleteAllFields': 'Please fill in all required fields',
      'markAllAsRead': 'Mark All as Read',
      'idCardInfo': 'ID Card Information',
      'idCard': 'ID Card',
      'issuedDate': 'Issued Date',
      'birthday': 'Birthday',
      'bankAccount': 'Bank Account',
      'accountNumber': 'Account Number',
      'accountHolder': 'Account Holder',
      'noInformation': 'No information',
      'enterField': 'Enter',
      'confirmDeleteBank': 'Are you sure you want to delete this bank account?',
      'deleteBankSuccess': 'Bank account deleted successfully',
      'errorDeletingBank': 'Error deleting account',
      'noBankAccounts': 'No bank accounts yet',
      'deleteAccount': 'Delete account',
      'unsavedData': 'Unsaved data',
      'unsavedChangesMessage':
          'You have unsaved information. Do you want to leave?',
      'stayHere': 'Stay here',
      'leave': 'Leave',
      'unsavedChangesExitMessage':
          'You have unsaved information. Are you sure you want to exit?',
      'errorLoadingInfo': 'Error loading information',
      'errorUpdatingInfo': 'Error updating information',
      'approvalStatus': 'Approval Status',
      'notifications': 'Notifications',
      'notes': 'Notes',
      'home': 'Home',
      'listView': 'List',
      'pendingList': 'Pending List',
      'profile': 'Profile',
      'loadingPageMessage': 'Loading page, please wait...',
      'newNotification': 'New notification',
      'checkAppForDetails': 'Check the app for details',
      'newNotificationForeground': 'New notification (Foreground)',
      'paymentFormTitle': 'Payment Form',
      'editPayment': 'Edit Payment',
      'invoiceFormTitle': 'Invoice Form',
      'createNote': 'Create Note',
      'personalProfile': 'Personal Profile',
      'waitingDataLoad': 'Waiting for data to load...',
      'receivedNotificationUpdate': 'Received notification update',
      'unreadNotifications': 'Unread notifications',
      'pleaseSelectOrganization': 'Please select organization',
      'pleaseEnterEmail': 'Please enter email',
      'loginFailed': 'Login failed',
      'loginError': 'Login error',
      'connectionError': 'Connection error',
      'cannotConnectToServer': 'Cannot connect to server',
      'organizationNotFound': 'Organization not found',
      'enterEmail': 'Enter email',
      'emailValidationError': 'Email validation error',
      'enterPassword': 'Enter password',
      'login': 'Login',
      'cannotLoadUserData': 'Cannot load user data',
      'cannotLoadCurrencyData': 'Cannot load currency data',
      'cannotSelectFile': 'Cannot select file',
      'invalidToken': 'Invalid token',
      'invalidTokenLogin': 'Invalid token, please login again',
      'appliedBankInfo': 'Applied bank information',
      'beneficiaryName': 'Beneficiary Name',
      'enterBeneficiaryName': 'Enter beneficiary name',
      'searchBankInfoHelper': 'Search bank information helper',
      'pleaseEnterBeneficiaryName': 'Please enter beneficiary name',
      'connectionTimeout': 'Connection timeout',
      'noServerResponse': 'No server response',
      'serverError': 'Server error',
      'requestCancelled': 'Request cancelled',
      'cannotConnectServer': 'Cannot connect to server',
      'unknownError': 'Unknown error',
      'generalError': 'General error',
      'confirm': 'Confirm',
      'unsavedChangesConfirm': 'Unsaved changes confirmation',
      'no': 'No',
      'yes': 'Yes',
      'processingWait': 'Processing, please wait',
      'enterInformation': 'Enter information',
      'paymentTitle': 'Payment Title',
      'invoiceTitle': 'Invoice Title',
      'pleaseEnterPaymentTitle': 'Please enter payment title',
      'pleaseEnterAccountNumber': 'Please enter account number',
      'bank': 'Bank',
      'pleaseEnterBankName': 'Please enter bank name',
      'paymentCurrencyType': 'Payment currency type',
      'pleaseSelectCurrencyType': 'Please select currency type',
      'swiftCode': 'SWIFT Code',
      'pleaseEnterSwiftCode': 'Please enter SWIFT code',
      'pleaseEnterAmount': 'Please enter amount',
      'pleaseEnterValidNumber': 'Please enter valid number',
      'paymentContent': 'Payment Content',
      'pleaseEnterPaymentContent': 'Please enter payment content',
      'attachDocuments': 'Attach Documents',
      'selectedFiles': 'Selected Files',
      'success': 'Success',
      'updateRequestSuccess': 'Update request successful',
      'createPaymentRequestSuccess': 'Create payment request successful',
      'pleaseCompleteAllFieldsCorrectly':
          'Please complete all fields correctly',
      'invalidTokenPleaseLogin': 'Invalid token. Please login again.',
      'processingPleaseWait': 'Processing, please wait a moment...',
      'apiErrorCode': 'API Error Code',
      'errorSubmittingForm': 'Error submitting form',
      'buyerName': 'Buyer Name',
      'pleaseEnterBuyerName': 'Please enter buyer name',
      'taxCode': 'Tax Code',
      'pleaseEnterTaxCode': 'Please enter tax code',
      'quantity': 'Quantity',
      'enterQuantity': 'Enter quantity',
      'pleaseEnterQuantity': 'Please enter quantity',
      'quantityMustBeGreaterThanZero': 'Quantity must be greater than 0',
      'unitPrice': 'Unit Price',
      'enterUnitPrice': 'Enter unit price',
      'pleaseEnterUnitPrice': 'Please enter unit price',
      'unitPriceMustBeGreaterThanZero': 'Unit price must be greater than 0',
      'taxRate': 'Tax Rate',
      'pleaseSelectTaxRate': 'Please select tax rate',
      'subtotal': 'Subtotal',
      'totalAmount': 'Total Amount',
      'invoiceContent': 'Invoice Content',
      'enterInvoiceContent': 'Enter invoice content',
      'pleaseEnterInvoiceContent': 'Please enter invoice content',
      'saveDraftSuccess': 'Save draft successful',
      'sendRequestSuccess': 'Send request successful',
      'selectNotificationDate': 'Select notification date',
      'select': 'Select',
      'invalidDateFormat': 'Invalid date format',
      'invalidDate': 'Invalid date',
      'enterDate': 'Enter date',
      'futureDateOnly': 'Future date only',
      'close': 'Close',
      'cannotSelectImage': 'Cannot select image',
      'pleaseCompleteRequiredFields': 'Please complete required fields',
      'userNotFound': 'User not found',
      'userIdNotFound': 'User ID not found',
      'organizationInfoNotFound': 'Organization information not found',
      'noteSavedSuccess': 'Note saved successfully',
      'errorSavingNote': 'Error saving note',
      'createNoteTitle': 'Create Note',
      'noteInformation': 'Note Information',
      'noteTitleLabel': 'Note Title',
      'enterNoteTitle': 'Enter note title',
      'pleaseEnterNoteTitle': 'Please enter note title',
      'titleMinLength': 'Title minimum length',
      'notificationDateLabel': 'Notification Date',
      'selectDateFutureOnly': 'Select date (future only)',
      'futureDateHelper': 'Future date helper',
      'pleaseSelectNotificationDate': 'Please select notification date',
      'futureDateValidation': 'Future date validation',
      'noteContentLabel': 'Note Content',
      'enterNoteContent': 'Enter note content',
      'pleaseEnterNoteContent': 'Please enter note content',
      'attachedImages': 'Attached Images',
      'selectImages': 'Select Images',
      'selectedImages': 'Selected Images',
      'saveNote': 'Save Note',
      'errorOccurred': 'An error occurred',
      'tryAgain': 'Try Again',
      'noNotifications': 'No notifications',
      'viewForm': 'View Form',
      'invalidFormType': 'Invalid form type',
      'cannotLoadFormInfo': 'Cannot load form information',
      'storagePermissionRequired':
          'Storage permission required to download file',
      'downloadingFile': 'Downloading file...',
      'cannotDownloadFile': 'Cannot download file',
      'cannotOpenFileWeb': 'Cannot open file on web',
      'paymentDetails': 'Payment Details',
      'paymentAmount': 'Payment Amount',
      'downloadFile': 'Download file',
      'download': 'Download',
      'doYouWantToDownload': 'Do you want to download',
      'downloaded': 'Downloaded',
      'pathLabel': 'Path',
      'createdBy': 'Created By',
      'createdAt': 'Created At',
      'updatedAt': 'Updated At',
      'userLabel': 'User',
      'historyLogs': 'History Logs',
      'bankName': 'Bank Name',
      'title': 'Title',
      'description': 'Description',
      'currency': 'Currency',
      'notAvailable': 'N/A',
      'approve': 'Approve',
      'reject': 'Reject',
      'paymentDetailsTitle': 'Payment Details',
      'invoiceDetailsTitle': 'Invoice Details',
      'invoiceDetails': 'Invoice Details',
      'editInvoice': 'Edit Invoice',
      'approveRequestSuccess': 'Request approved successfully',
      'rejectRequestSuccess': 'Request rejected successfully'
    },
    'vi': {
      'personalInfo': 'Thông tin cá nhân',
      'fullName': 'Họ và tên',
      'email': 'Email',
      'phoneNumber': 'Số điện thoại',
      'companyname': 'Tên công ty',
      'saveChanges': 'Lưu thay đổi',
      'updateSuccess': 'Thông tin đã được cập nhật',
      'logout': 'Đăng xuất',
      'cancel': 'Hủy',
      'confirmLogout': 'Bạn có chắc chắn muốn đăng xuất?',
      'language': 'Ngôn ngữ',
      'vietnamese': 'Tiếng Việt',
      'english': 'Tiếng Anh',
      'amount': 'Số tiền',
      'status': 'Trạng thái',
      'category': 'Danh mục',
      'attachments': 'Tệp đính kèm',
      'confirmDelete': 'Xác nhận xóa',
      'delete': 'Xóa',
      'edit': 'Sửa',
      'searchHint': 'Tìm kiếm...',
      'list': 'Danh Sách',
      'filterTitle': 'Bộ lọc',
      'selectDate': 'Chọn ngày',
      'date': 'Ngày',
      'clearFilter': 'Xóa bộ lọc',
      'applyFilter': 'Áp dụng',
      'filterTooltip': 'Lọc danh sách',
      'pending': 'Đang chờ',
      'approved': 'Đã duyệt',
      'rejected': 'Từ chối',
      'statusApproved': 'Đã duyệt',
      'statusPendingApprover': 'Chờ duyệt',
      'statusPendingAccountant': 'Chờ kế toán',
      'statusFailed': 'Từ chối',
      'statusDraft': 'Nháp',
      'statusCancelled': 'Hủy',
      'notification': 'Thông báo',
      'unreadNotesTitle': 'Thông báo ghi chú chưa đọc',
      'notesCount': 'ghi chú',
      'ok': 'OK',
      'details': 'Chi tiết',
      'selectFromGallery': 'Chọn từ thư viện',
      'takeNewPhoto': 'Chụp ảnh mới',
      'selectBank': 'Chọn ngân hàng',
      'errorLoadingProfile': 'Không thể tải thông tin người dùng',
      'invalidEmail': 'Email phải có định dạng @gmail.com',
      'pleaseEnterPassword': 'Vui lòng nhập mật khẩu',
      'pleaseEnterNewPassword': 'Vui lòng nhập mật khẩu mới',
      'pleaseConfirmPassword': 'Vui lòng xác nhận mật khẩu mới',
      'passwordMismatch': 'Mật khẩu xác nhận không khớp',
      'approveSuccess': 'Đã duyệt yêu cầu thành công',
      'rejectSuccess': 'Đã từ chối yêu cầu thành công',
      'pleaseEnterReason': 'Vui lòng nhập lý do từ chối',
      'invalidOTP': 'Mã OTP không chính xác!',
      'pleaseCompleteAllFields':
          'Vui lòng điền đầy đủ tất cả các trường thông tin',
      'markAllAsRead': 'Đã đọc tất cả',
      'idCardInfo': 'Thông tin CCCD',
      'idCard': 'CCCD',
      'issuedDate': 'Ngày cấp',
      'birthday': 'Ngày sinh',
      'bankAccount': 'Tài khoản ngân hàng',
      'accountNumber': 'Số tài khoản',
      'accountHolder': 'Chủ tài khoản',
      'noInformation': 'Không có thông tin',
      'enterField': 'Nhập',
      'confirmDeleteBank': 'Bạn có chắc chắn muốn xóa tài khoản ngân hàng này?',
      'deleteBankSuccess': 'Xóa tài khoản ngân hàng thành công',
      'errorDeletingBank': 'Lỗi xóa tài khoản',
      'noBankAccounts': 'Chưa có tài khoản ngân hàng nào',
      'deleteAccount': 'Xóa tài khoản',
      'unsavedData': 'Dữ liệu chưa được lưu',
      'unsavedChangesMessage':
          'Bạn đang có thông tin chưa lưu. Bạn có muốn rời đi không?',
      'stayHere': 'Ở lại',
      'leave': 'Rời đi',
      'unsavedChangesExitMessage':
          'Bạn có thông tin chưa lưu. Bạn có chắc chắn muốn thoát không?',
      'errorLoadingInfo': 'Lỗi tải thông tin',
      'errorUpdatingInfo': 'Lỗi cập nhật thông tin',
      'approvalStatus': 'Trạng thái phê duyệt',
      'notifications': 'Thông báo',
      'notes': 'Ghi chú',
      'home': 'Trang chủ',
      'listView': 'Danh sách',
      'pendingList': 'Danh sách Pending',
      'profile': 'Hồ sơ',
      'loadingPageMessage': 'Đang chuyển trang, vui lòng đợi...',
      'newNotification': 'Thông báo mới',
      'checkAppForDetails': 'Kiểm tra ứng dụng để biết chi tiết',
      'newNotificationForeground': 'Thông báo mới (Foreground)',
      'paymentFormTitle': 'Mẫu thanh toán',
      'editPayment': 'Chỉnh sửa thanh toán',
      'invoiceFormTitle': 'Mẫu hóa đơn',
      'createNote': 'Tạo ghi chú',
      'personalProfile': 'Hồ sơ cá nhân',
      'waitingDataLoad': 'Vui lòng đợi dữ liệu đang tải xong...',
      'receivedNotificationUpdate': 'Đã nhận cập nhật thông báo',
      'unreadNotifications': 'Thông báo chưa đọc',
      'pleaseSelectOrganization': 'Vui lòng chọn tổ chức',
      'pleaseEnterEmail': 'Vui lòng nhập email',
      'loginFailed': 'Đăng nhập thất bại',
      'loginError': 'Lỗi đăng nhập',
      'connectionError': 'Lỗi kết nối',
      'cannotConnectToServer': 'Không thể kết nối đến máy chủ',
      'organizationNotFound': 'Không tìm thấy tổ chức',
      'enterEmail': 'Nhập email',
      'emailValidationError': 'Lỗi xác thực email',
      'enterPassword': 'Nhập mật khẩu',
      'login': 'Đăng nhập',
      'cannotLoadUserData': 'Không thể tải dữ liệu người dùng',
      'cannotLoadCurrencyData': 'Không thể tải dữ liệu tiền tệ',
      'cannotSelectFile': 'Không thể chọn tệp',
      'invalidToken': 'Token không hợp lệ',
      'invalidTokenLogin': 'Token không hợp lệ, vui lòng đăng nhập lại',
      'appliedBankInfo': 'Thông tin ngân hàng đã áp dụng',
      'beneficiaryName': 'Tên người thụ hưởng',
      'enterBeneficiaryName': 'Nhập tên người thụ hưởng',
      'searchBankInfoHelper': 'Trợ giúp tìm kiếm thông tin ngân hàng',
      'pleaseEnterBeneficiaryName': 'Vui lòng nhập tên người thụ hưởng',
      'connectionTimeout': 'Hết thời gian kết nối',
      'noServerResponse': 'Không có phản hồi từ máy chủ',
      'serverError': 'Lỗi máy chủ',
      'requestCancelled': 'Yêu cầu đã bị hủy',
      'cannotConnectServer': 'Không thể kết nối máy chủ',
      'unknownError': 'Lỗi không xác định',
      'generalError': 'Lỗi chung',
      'confirm': 'Xác nhận',
      'unsavedChangesConfirm': 'Xác nhận thay đổi chưa lưu',
      'no': 'Không',
      'yes': 'Có',
      'processingWait': 'Đang xử lý, vui lòng đợi',
      'enterInformation': 'Nhập thông tin',
      'paymentTitle': 'Tiêu đề thanh toán',
      'invoiceTitle': 'Tiêu đề hóa đơn',
      'pleaseEnterPaymentTitle': 'Vui lòng nhập tiêu đề thanh toán',
      'pleaseEnterAccountNumber': 'Vui lòng nhập số tài khoản',
      'bank': 'Ngân hàng',
      'pleaseEnterBankName': 'Vui lòng nhập tên ngân hàng',
      'paymentCurrencyType': 'Loại tiền tệ thanh toán',
      'pleaseSelectCurrencyType': 'Vui lòng chọn loại tiền tệ',
      'swiftCode': 'Mã SWIFT',
      'pleaseEnterSwiftCode': 'Vui lòng nhập mã SWIFT',
      'pleaseEnterAmount': 'Vui lòng nhập số tiền',
      'pleaseEnterValidNumber': 'Vui lòng nhập số hợp lệ',
      'paymentContent': 'Nội dung thanh toán',
      'pleaseEnterPaymentContent': 'Vui lòng nhập nội dung thanh toán',
      'attachDocuments': 'Đính kèm tài liệu',
      'selectedFiles': 'Tệp đã chọn',
      'success': 'Thành công',
      'updateRequestSuccess': 'Cập nhật yêu cầu thành công',
      'createPaymentRequestSuccess': 'Tạo yêu cầu thanh toán thành công',
      'pleaseCompleteAllFieldsCorrectly':
          'Vui lòng hoàn thành tất cả các trường một cách chính xác',
      'invalidTokenPleaseLogin': 'Token không hợp lệ. Vui lòng đăng nhập lại.',
      'processingPleaseWait': 'Đang xử lý, vui lòng đợi trong giây lát...',
      'apiErrorCode': 'Mã lỗi API',
      'errorSubmittingForm': 'Lỗi gửi biểu mẫu',
      'buyerName': 'Tên người mua',
      'pleaseEnterBuyerName': 'Vui lòng nhập tên người mua',
      'taxCode': 'Mã số thuế',
      'pleaseEnterTaxCode': 'Vui lòng nhập mã số thuế',
      'quantity': 'Số lượng',
      'enterQuantity': 'Nhập số lượng',
      'pleaseEnterQuantity': 'Vui lòng nhập số lượng',
      'quantityMustBeGreaterThanZero': 'Số lượng phải lớn hơn 0',
      'unitPrice': 'Đơn giá',
      'enterUnitPrice': 'Nhập đơn giá',
      'pleaseEnterUnitPrice': 'Vui lòng nhập đơn giá',
      'unitPriceMustBeGreaterThanZero': 'Đơn giá phải lớn hơn 0',
      'taxRate': 'Thuế suất',
      'pleaseSelectTaxRate': 'Vui lòng chọn thuế suất',
      'subtotal': 'Tạm tính',
      'totalAmount': 'Tổng tiền',
      'invoiceContent': 'Nội dung hóa đơn',
      'enterInvoiceContent': 'Nhập nội dung hóa đơn',
      'pleaseEnterInvoiceContent': 'Vui lòng nhập nội dung hóa đơn',
      'saveDraftSuccess': 'Lưu nháp thành công',
      'sendRequestSuccess': 'Gửi yêu cầu thành công',
      'selectNotificationDate': 'Chọn ngày thông báo',
      'select': 'Chọn',
      'invalidDateFormat': 'Định dạng ngày không hợp lệ',
      'invalidDate': 'Ngày không hợp lệ',
      'enterDate': 'Nhập ngày',
      'futureDateOnly': 'Chỉ ngày tương lai',
      'close': 'Đóng',
      'cannotSelectImage': 'Không thể chọn hình ảnh',
      'pleaseCompleteRequiredFields': 'Vui lòng hoàn thành các trường bắt buộc',
      'userNotFound': 'Không tìm thấy người dùng',
      'userIdNotFound': 'Không tìm thấy ID người dùng',
      'organizationInfoNotFound': 'Không tìm thấy thông tin tổ chức',
      'noteSavedSuccess': 'Ghi chú đã được lưu thành công',
      'errorSavingNote': 'Lỗi lưu ghi chú',
      'createNoteTitle': 'Tạo ghi chú',
      'noteInformation': 'Thông tin ghi chú',
      'noteTitleLabel': 'Tiêu đề ghi chú',
      'enterNoteTitle': 'Nhập tiêu đề ghi chú',
      'pleaseEnterNoteTitle': 'Vui lòng nhập tiêu đề ghi chú',
      'titleMinLength': 'Độ dài tối thiểu của tiêu đề',
      'notificationDateLabel': 'Ngày thông báo',
      'selectDateFutureOnly': 'Chọn ngày (chỉ tương lai)',
      'futureDateHelper': 'Trợ giúp ngày tương lai',
      'pleaseSelectNotificationDate': 'Vui lòng chọn ngày thông báo',
      'futureDateValidation': 'Xác thực ngày tương lai',
      'noteContentLabel': 'Nội dung ghi chú',
      'enterNoteContent': 'Nhập nội dung ghi chú',
      'pleaseEnterNoteContent': 'Vui lòng nhập nội dung ghi chú',
      'attachedImages': 'Hình ảnh đính kèm',
      'selectImages': 'Chọn hình ảnh',
      'selectedImages': 'Hình ảnh đã chọn',
      'saveNote': 'Lưu ghi chú',
      'errorOccurred': 'Đã xảy ra lỗi',
      'tryAgain': 'Thử lại',
      'noNotifications': 'Không có thông báo',
      'viewForm': 'Xem form',
      'invalidFormType': 'Loại form không hợp lệ',
      'cannotLoadFormInfo': 'Không thể tải thông tin form',
      'storagePermissionRequired': 'Cần cấp quyền truy cập bộ nhớ để tải file',
      'downloadingFile': 'Đang tải file...',
      'cannotDownloadFile': 'Không thể tải file',
      'cannotOpenFileWeb': 'Không thể mở file trên web',
      'paymentDetails': 'Chi tiết thanh toán',
      'paymentAmount': 'Số tiền thanh toán',
      'downloadFile': 'Download file',
      'download': 'Tải xuống',
      'doYouWantToDownload': 'Bạn có muốn tải xuống',
      'downloaded': 'Đã tải xuống',
      'pathLabel': 'Đường dẫn',
      'createdBy': 'Tạo bởi',
      'createdAt': 'Tạo lúc',
      'updatedAt': 'Cập nhật lúc',
      'userLabel': 'Người dùng',
      'historyLogs': 'Lịch sử hoạt động',
      'bankName': 'Tên ngân hàng',
      'title': 'Tiêu đề',
      'description': 'Mô tả',
      'currency': 'Loại tiền',
      'notAvailable': 'Không có',
      'approve': 'Duyệt',
      'reject': 'Từ chối',
      'paymentDetailsTitle': 'Chi tiết thanh toán',
      'invoiceDetailsTitle': 'Chi tiết hóa đơn',
      'invoiceDetails': 'Chi tiết hóa đơn',
      'editInvoice': 'Chỉnh sửa hóa đơn',
      'approveRequestSuccess': 'Đã duyệt yêu cầu thành công',
      'rejectRequestSuccess': 'Đã từ chối yêu cầu thành công'
    }
  };

  String get personalInfo =>
      _localizedValues[locale.languageCode]!['personalInfo']!;
  String get fullName => _localizedValues[locale.languageCode]!['fullName']!;
  String get email => _localizedValues[locale.languageCode]!['email']!;
  String get phoneNumber =>
      _localizedValues[locale.languageCode]!['phoneNumber']!;
  String get companyname =>
      _localizedValues[locale.languageCode]!['companyname']!;
  String get saveChanges =>
      _localizedValues[locale.languageCode]!['saveChanges']!;
  String get updateSuccess =>
      _localizedValues[locale.languageCode]!['updateSuccess']!;
  String get logout => _localizedValues[locale.languageCode]!['logout']!;
  String get cancel => _localizedValues[locale.languageCode]!['cancel']!;
  String get confirmLogout =>
      _localizedValues[locale.languageCode]!['confirmLogout']!;
  String get language => _localizedValues[locale.languageCode]!['language']!;
  String get vietnamese =>
      _localizedValues[locale.languageCode]!['vietnamese']!;
  String get english => _localizedValues[locale.languageCode]!['english']!;
  String get amount => _localizedValues[locale.languageCode]!['amount']!;
  String get status => _localizedValues[locale.languageCode]!['status']!;
  String get category => _localizedValues[locale.languageCode]!['category']!;
  String get attachments =>
      _localizedValues[locale.languageCode]!['attachments']!;
  String get confirmDelete =>
      _localizedValues[locale.languageCode]!['confirmDelete']!;
  String get delete => _localizedValues[locale.languageCode]!['delete']!;
  String get edit => _localizedValues[locale.languageCode]!['edit']!;
  String get searchHint =>
      _localizedValues[locale.languageCode]!['searchHint']!;
  String get list => _localizedValues[locale.languageCode]!['list']!;
  String get filterTitle =>
      _localizedValues[locale.languageCode]!['filterTitle']!;
  String get selectDate =>
      _localizedValues[locale.languageCode]!['selectDate']!;
  String get date => _localizedValues[locale.languageCode]!['date']!;
  String get clearFilter =>
      _localizedValues[locale.languageCode]!['clearFilter']!;
  String get applyFilter =>
      _localizedValues[locale.languageCode]!['applyFilter']!;
  String get filterTooltip =>
      _localizedValues[locale.languageCode]!['filterTooltip']!;
  String get pending => _localizedValues[locale.languageCode]!['pending']!;
  String get approved => _localizedValues[locale.languageCode]!['approved']!;
  String get rejected => _localizedValues[locale.languageCode]!['rejected']!;
  String get statusApproved =>
      _localizedValues[locale.languageCode]!['statusApproved']!;
  String get statusPendingApprover =>
      _localizedValues[locale.languageCode]!['statusPendingApprover']!;
  String get statusPendingAccountant =>
      _localizedValues[locale.languageCode]!['statusPendingAccountant']!;
  String get statusFailed =>
      _localizedValues[locale.languageCode]!['statusFailed']!;
  String get statusDraft =>
      _localizedValues[locale.languageCode]!['statusDraft']!;
  String get statusCancelled =>
      _localizedValues[locale.languageCode]!['statusCancelled']!;
  String get notification =>
      _localizedValues[locale.languageCode]!['notification']!;
  String get unreadNotesTitle =>
      _localizedValues[locale.languageCode]!['unreadNotesTitle']!;
  String get notesCount =>
      _localizedValues[locale.languageCode]!['notesCount']!;
  String get ok => _localizedValues[locale.languageCode]!['ok']!;
  String get details => _localizedValues[locale.languageCode]!['details']!;
  String get selectFromGallery =>
      _localizedValues[locale.languageCode]!['selectFromGallery']!;
  String get takeNewPhoto =>
      _localizedValues[locale.languageCode]!['takeNewPhoto']!;
  String get selectBank =>
      _localizedValues[locale.languageCode]!['selectBank']!;
  String get errorLoadingProfile =>
      _localizedValues[locale.languageCode]!['errorLoadingProfile']!;
  String get invalidEmail =>
      _localizedValues[locale.languageCode]!['invalidEmail']!;
  String get pleaseEnterPassword =>
      _localizedValues[locale.languageCode]!['pleaseEnterPassword']!;
  String get pleaseEnterNewPassword =>
      _localizedValues[locale.languageCode]!['pleaseEnterNewPassword']!;
  String get pleaseConfirmPassword =>
      _localizedValues[locale.languageCode]!['pleaseConfirmPassword']!;
  String get passwordMismatch =>
      _localizedValues[locale.languageCode]!['passwordMismatch']!;
  String get approveSuccess =>
      _localizedValues[locale.languageCode]!['approveSuccess']!;
  String get rejectSuccess =>
      _localizedValues[locale.languageCode]!['rejectSuccess']!;
  String get pleaseEnterReason =>
      _localizedValues[locale.languageCode]!['pleaseEnterReason']!;
  String get invalidOTP =>
      _localizedValues[locale.languageCode]!['invalidOTP']!;
  String get pleaseCompleteAllFields =>
      _localizedValues[locale.languageCode]!['pleaseCompleteAllFields']!;
  String get markAllAsRead =>
      _localizedValues[locale.languageCode]!['markAllAsRead']!;
  String get idCardInfo =>
      _localizedValues[locale.languageCode]!['idCardInfo']!;
  String get idCard => _localizedValues[locale.languageCode]!['idCard']!;
  String get issuedDate =>
      _localizedValues[locale.languageCode]!['issuedDate']!;
  String get birthday => _localizedValues[locale.languageCode]!['birthday']!;
  String get bankAccount =>
      _localizedValues[locale.languageCode]!['bankAccount']!;
  String get accountNumber =>
      _localizedValues[locale.languageCode]!['accountNumber']!;
  String get accountHolder =>
      _localizedValues[locale.languageCode]!['accountHolder']!;
  String get noInformation =>
      _localizedValues[locale.languageCode]!['noInformation']!;
  String get enterField =>
      _localizedValues[locale.languageCode]!['enterField']!;
  String get confirmDeleteBank =>
      _localizedValues[locale.languageCode]!['confirmDeleteBank']!;
  String get deleteBankSuccess =>
      _localizedValues[locale.languageCode]!['deleteBankSuccess']!;
  String get errorDeletingBank =>
      _localizedValues[locale.languageCode]!['errorDeletingBank']!;
  String get noBankAccounts =>
      _localizedValues[locale.languageCode]!['noBankAccounts']!;
  String get deleteAccount =>
      _localizedValues[locale.languageCode]!['deleteAccount']!;
  String get unsavedData =>
      _localizedValues[locale.languageCode]!['unsavedData']!;
  String get unsavedChangesMessage =>
      _localizedValues[locale.languageCode]!['unsavedChangesMessage']!;
  String get stayHere => _localizedValues[locale.languageCode]!['stayHere']!;
  String get leave => _localizedValues[locale.languageCode]!['leave']!;
  String get unsavedChangesExitMessage =>
      _localizedValues[locale.languageCode]!['unsavedChangesExitMessage']!;
  String get errorLoadingInfo =>
      _localizedValues[locale.languageCode]!['errorLoadingInfo']!;
  String get errorUpdatingInfo =>
      _localizedValues[locale.languageCode]!['errorUpdatingInfo']!;
  String get approvalStatus =>
      _localizedValues[locale.languageCode]!['approvalStatus']!;
  String get notifications =>
      _localizedValues[locale.languageCode]!['notifications']!;
  String get notes => _localizedValues[locale.languageCode]!['notes']!;
  String get home => _localizedValues[locale.languageCode]!['home']!;
  String get listView => _localizedValues[locale.languageCode]!['listView']!;
  String get pendingList =>
      _localizedValues[locale.languageCode]!['pendingList']!;
  String get profile => _localizedValues[locale.languageCode]!['profile']!;
  String get loadingPageMessage =>
      _localizedValues[locale.languageCode]!['loadingPageMessage']!;
  String get newNotification =>
      _localizedValues[locale.languageCode]!['newNotification']!;
  String get checkAppForDetails =>
      _localizedValues[locale.languageCode]!['checkAppForDetails']!;
  String get newNotificationForeground =>
      _localizedValues[locale.languageCode]!['newNotificationForeground']!;
  String get paymentFormTitle =>
      _localizedValues[locale.languageCode]!['paymentFormTitle']!;
  String get editPayment =>
      _localizedValues[locale.languageCode]!['editPayment']!;
  String get invoiceFormTitle =>
      _localizedValues[locale.languageCode]!['invoiceFormTitle']!;
  String get createNote =>
      _localizedValues[locale.languageCode]!['createNote']!;
  String get personalProfile =>
      _localizedValues[locale.languageCode]!['personalProfile']!;
  String get waitingDataLoad =>
      _localizedValues[locale.languageCode]!['waitingDataLoad']!;
  String get receivedNotificationUpdate =>
      _localizedValues[locale.languageCode]!['receivedNotificationUpdate']!;
  String get unreadNotifications =>
      _localizedValues[locale.languageCode]!['unreadNotifications']!;
  String get pleaseSelectOrganization =>
      _localizedValues[locale.languageCode]!['pleaseSelectOrganization']!;
  String get pleaseEnterEmail =>
      _localizedValues[locale.languageCode]!['pleaseEnterEmail']!;
  String get loginFailed =>
      _localizedValues[locale.languageCode]!['loginFailed']!;
  String get loginError =>
      _localizedValues[locale.languageCode]!['loginError']!;
  String get connectionError =>
      _localizedValues[locale.languageCode]!['connectionError']!;
  String get cannotConnectToServer =>
      _localizedValues[locale.languageCode]!['cannotConnectToServer']!;
  String get organizationNotFound =>
      _localizedValues[locale.languageCode]!['organizationNotFound']!;
  String get enterEmail =>
      _localizedValues[locale.languageCode]!['enterEmail']!;
  String get emailValidationError =>
      _localizedValues[locale.languageCode]!['emailValidationError']!;
  String get enterPassword =>
      _localizedValues[locale.languageCode]!['enterPassword']!;
  String get login => _localizedValues[locale.languageCode]!['login']!;
  String get cannotLoadUserData =>
      _localizedValues[locale.languageCode]!['cannotLoadUserData']!;
  String get cannotLoadCurrencyData =>
      _localizedValues[locale.languageCode]!['cannotLoadCurrencyData']!;
  String get cannotSelectFile =>
      _localizedValues[locale.languageCode]!['cannotSelectFile']!;
  String get invalidToken =>
      _localizedValues[locale.languageCode]!['invalidToken']!;
  String get invalidTokenLogin =>
      _localizedValues[locale.languageCode]!['invalidTokenLogin']!;
  String get appliedBankInfo =>
      _localizedValues[locale.languageCode]!['appliedBankInfo']!;
  String get beneficiaryName =>
      _localizedValues[locale.languageCode]!['beneficiaryName']!;
  String get enterBeneficiaryName =>
      _localizedValues[locale.languageCode]!['enterBeneficiaryName']!;
  String get searchBankInfoHelper =>
      _localizedValues[locale.languageCode]!['searchBankInfoHelper']!;
  String get pleaseEnterBeneficiaryName =>
      _localizedValues[locale.languageCode]!['pleaseEnterBeneficiaryName']!;
  String get connectionTimeout =>
      _localizedValues[locale.languageCode]!['connectionTimeout']!;
  String get noServerResponse =>
      _localizedValues[locale.languageCode]!['noServerResponse']!;
  String get serverError =>
      _localizedValues[locale.languageCode]!['serverError']!;
  String get requestCancelled =>
      _localizedValues[locale.languageCode]!['requestCancelled']!;
  String get cannotConnectServer =>
      _localizedValues[locale.languageCode]!['cannotConnectServer']!;
  String get unknownError =>
      _localizedValues[locale.languageCode]!['unknownError']!;
  String get generalError =>
      _localizedValues[locale.languageCode]!['generalError']!;
  String get confirm => _localizedValues[locale.languageCode]!['confirm']!;
  String get unsavedChangesConfirm =>
      _localizedValues[locale.languageCode]!['unsavedChangesConfirm']!;
  String get no => _localizedValues[locale.languageCode]!['no']!;
  String get yes => _localizedValues[locale.languageCode]!['yes']!;
  String get processingWait =>
      _localizedValues[locale.languageCode]!['processingWait']!;
  String get enterInformation =>
      _localizedValues[locale.languageCode]!['enterInformation']!;
  String get paymentTitle =>
      _localizedValues[locale.languageCode]!['paymentTitle']!;
  String get invoiceTitle =>
      _localizedValues[locale.languageCode]!['invoiceTitle']!;
  String get pleaseEnterPaymentTitle =>
      _localizedValues[locale.languageCode]!['pleaseEnterPaymentTitle']!;
  String get pleaseEnterAccountNumber =>
      _localizedValues[locale.languageCode]!['pleaseEnterAccountNumber']!;
  String get bank => _localizedValues[locale.languageCode]!['bank']!;
  String get pleaseEnterBankName =>
      _localizedValues[locale.languageCode]!['pleaseEnterBankName']!;
  String get paymentCurrencyType =>
      _localizedValues[locale.languageCode]!['paymentCurrencyType']!;
  String get pleaseSelectCurrencyType =>
      _localizedValues[locale.languageCode]!['pleaseSelectCurrencyType']!;
  String get swiftCode => _localizedValues[locale.languageCode]!['swiftCode']!;
  String get pleaseEnterSwiftCode =>
      _localizedValues[locale.languageCode]!['pleaseEnterSwiftCode']!;
  String get pleaseEnterAmount =>
      _localizedValues[locale.languageCode]!['pleaseEnterAmount']!;
  String get pleaseEnterValidNumber =>
      _localizedValues[locale.languageCode]!['pleaseEnterValidNumber']!;
  String get paymentContent =>
      _localizedValues[locale.languageCode]!['paymentContent']!;
  String get pleaseEnterPaymentContent =>
      _localizedValues[locale.languageCode]!['pleaseEnterPaymentContent']!;
  String get attachDocuments =>
      _localizedValues[locale.languageCode]!['attachDocuments']!;
  String get selectedFiles =>
      _localizedValues[locale.languageCode]!['selectedFiles']!;
  String get success => _localizedValues[locale.languageCode]!['success']!;
  String get updateRequestSuccess =>
      _localizedValues[locale.languageCode]!['updateRequestSuccess']!;
  String get createPaymentRequestSuccess =>
      _localizedValues[locale.languageCode]!['createPaymentRequestSuccess']!;
  String get pleaseCompleteAllFieldsCorrectly => _localizedValues[
      locale.languageCode]!['pleaseCompleteAllFieldsCorrectly']!;
  String get invalidTokenPleaseLogin =>
      _localizedValues[locale.languageCode]!['invalidTokenPleaseLogin']!;
  String get processingPleaseWait =>
      _localizedValues[locale.languageCode]!['processingPleaseWait']!;
  String get apiErrorCode =>
      _localizedValues[locale.languageCode]!['apiErrorCode']!;
  String get errorSubmittingForm =>
      _localizedValues[locale.languageCode]!['errorSubmittingForm']!;
  String get buyerName => _localizedValues[locale.languageCode]!['buyerName']!;
  String get pleaseEnterBuyerName =>
      _localizedValues[locale.languageCode]!['pleaseEnterBuyerName']!;
  String get taxCode => _localizedValues[locale.languageCode]!['taxCode']!;
  String get pleaseEnterTaxCode =>
      _localizedValues[locale.languageCode]!['pleaseEnterTaxCode']!;
  String get quantity => _localizedValues[locale.languageCode]!['quantity']!;
  String get enterQuantity =>
      _localizedValues[locale.languageCode]!['enterQuantity']!;
  String get pleaseEnterQuantity =>
      _localizedValues[locale.languageCode]!['pleaseEnterQuantity']!;
  String get quantityMustBeGreaterThanZero =>
      _localizedValues[locale.languageCode]!['quantityMustBeGreaterThanZero']!;
  String get unitPrice => _localizedValues[locale.languageCode]!['unitPrice']!;
  String get enterUnitPrice =>
      _localizedValues[locale.languageCode]!['enterUnitPrice']!;
  String get pleaseEnterUnitPrice =>
      _localizedValues[locale.languageCode]!['pleaseEnterUnitPrice']!;
  String get unitPriceMustBeGreaterThanZero =>
      _localizedValues[locale.languageCode]!['unitPriceMustBeGreaterThanZero']!;
  String get taxRate => _localizedValues[locale.languageCode]!['taxRate']!;
  String get pleaseSelectTaxRate =>
      _localizedValues[locale.languageCode]!['pleaseSelectTaxRate']!;
  String get subtotal => _localizedValues[locale.languageCode]!['subtotal']!;
  String get totalAmount =>
      _localizedValues[locale.languageCode]!['totalAmount']!;
  String get invoiceContent =>
      _localizedValues[locale.languageCode]!['invoiceContent']!;
  String get enterInvoiceContent =>
      _localizedValues[locale.languageCode]!['enterInvoiceContent']!;
  String get pleaseEnterInvoiceContent =>
      _localizedValues[locale.languageCode]!['pleaseEnterInvoiceContent']!;
  String get saveDraftSuccess =>
      _localizedValues[locale.languageCode]!['saveDraftSuccess']!;
  String get sendRequestSuccess =>
      _localizedValues[locale.languageCode]!['sendRequestSuccess']!;
  String get selectNotificationDate =>
      _localizedValues[locale.languageCode]!['selectNotificationDate']!;
  String get select => _localizedValues[locale.languageCode]!['select']!;
  String get invalidDateFormat =>
      _localizedValues[locale.languageCode]!['invalidDateFormat']!;
  String get invalidDate =>
      _localizedValues[locale.languageCode]!['invalidDate']!;
  String get enterDate => _localizedValues[locale.languageCode]!['enterDate']!;
  String get futureDateOnly =>
      _localizedValues[locale.languageCode]!['futureDateOnly']!;
  String get close => _localizedValues[locale.languageCode]!['close']!;
  String get cannotSelectImage =>
      _localizedValues[locale.languageCode]!['cannotSelectImage']!;
  String get pleaseCompleteRequiredFields =>
      _localizedValues[locale.languageCode]!['pleaseCompleteRequiredFields']!;
  String get userNotFound =>
      _localizedValues[locale.languageCode]!['userNotFound']!;
  String get userIdNotFound =>
      _localizedValues[locale.languageCode]!['userIdNotFound']!;
  String get organizationInfoNotFound =>
      _localizedValues[locale.languageCode]!['organizationInfoNotFound']!;
  String get noteSavedSuccess =>
      _localizedValues[locale.languageCode]!['noteSavedSuccess']!;
  String get errorSavingNote =>
      _localizedValues[locale.languageCode]!['errorSavingNote']!;
  String get createNoteTitle =>
      _localizedValues[locale.languageCode]!['createNoteTitle']!;
  String get noteInformation =>
      _localizedValues[locale.languageCode]!['noteInformation']!;
  String get noteTitleLabel =>
      _localizedValues[locale.languageCode]!['noteTitleLabel']!;
  String get enterNoteTitle =>
      _localizedValues[locale.languageCode]!['enterNoteTitle']!;
  String get pleaseEnterNoteTitle =>
      _localizedValues[locale.languageCode]!['pleaseEnterNoteTitle']!;
  String get titleMinLength =>
      _localizedValues[locale.languageCode]!['titleMinLength']!;
  String get notificationDateLabel =>
      _localizedValues[locale.languageCode]!['notificationDateLabel']!;
  String get selectDateFutureOnly =>
      _localizedValues[locale.languageCode]!['selectDateFutureOnly']!;
  String get futureDateHelper =>
      _localizedValues[locale.languageCode]!['futureDateHelper']!;
  String get pleaseSelectNotificationDate =>
      _localizedValues[locale.languageCode]!['pleaseSelectNotificationDate']!;
  String get futureDateValidation =>
      _localizedValues[locale.languageCode]!['futureDateValidation']!;
  String get noteContentLabel =>
      _localizedValues[locale.languageCode]!['noteContentLabel']!;
  String get enterNoteContent =>
      _localizedValues[locale.languageCode]!['enterNoteContent']!;
  String get pleaseEnterNoteContent =>
      _localizedValues[locale.languageCode]!['pleaseEnterNoteContent']!;
  String get attachedImages =>
      _localizedValues[locale.languageCode]!['attachedImages']!;
  String get selectImages =>
      _localizedValues[locale.languageCode]!['selectImages']!;
  String get selectedImages =>
      _localizedValues[locale.languageCode]!['selectedImages']!;
  String get saveNote => _localizedValues[locale.languageCode]!['saveNote']!;
  String get errorOccurred =>
      _localizedValues[locale.languageCode]!['errorOccurred']!;
  String get tryAgain => _localizedValues[locale.languageCode]!['tryAgain']!;
  String get noNotifications =>
      _localizedValues[locale.languageCode]!['noNotifications']!;
  String get viewForm => _localizedValues[locale.languageCode]!['viewForm']!;
  String get invalidFormType =>
      _localizedValues[locale.languageCode]!['invalidFormType']!;
  String get cannotLoadFormInfo =>
      _localizedValues[locale.languageCode]!['cannotLoadFormInfo']!;
  String get storagePermissionRequired =>
      _localizedValues[locale.languageCode]!['storagePermissionRequired']!;
  String get downloadingFile =>
      _localizedValues[locale.languageCode]!['downloadingFile']!;
  String get cannotDownloadFile =>
      _localizedValues[locale.languageCode]!['cannotDownloadFile']!;
  String get cannotOpenFileWeb =>
      _localizedValues[locale.languageCode]!['cannotOpenFileWeb']!;
  String get paymentDetails =>
      _localizedValues[locale.languageCode]!['paymentDetails']!;
  String get paymentAmount =>
      _localizedValues[locale.languageCode]!['paymentAmount']!;
  String get downloadFile =>
      _localizedValues[locale.languageCode]!['downloadFile']!;
  String get download => _localizedValues[locale.languageCode]!['download']!;
  String get doYouWantToDownload =>
      _localizedValues[locale.languageCode]!['doYouWantToDownload']!;
  String get downloaded =>
      _localizedValues[locale.languageCode]!['downloaded']!;
  String get pathLabel => _localizedValues[locale.languageCode]!['pathLabel']!;
  String get createdBy => _localizedValues[locale.languageCode]!['createdBy']!;
  String get createdAt => _localizedValues[locale.languageCode]!['createdAt']!;
  String get updatedAt => _localizedValues[locale.languageCode]!['updatedAt']!;
  String get userLabel => _localizedValues[locale.languageCode]!['userLabel']!;
  String get historyLogs =>
      _localizedValues[locale.languageCode]!['historyLogs']!;
  String get bankName => _localizedValues[locale.languageCode]!['bankName']!;
  String get title => _localizedValues[locale.languageCode]!['title']!;
  String get description =>
      _localizedValues[locale.languageCode]!['description']!;
  String get currency => _localizedValues[locale.languageCode]!['currency']!;
  String get notAvailable =>
      _localizedValues[locale.languageCode]!['notAvailable']!;
  String get approve => _localizedValues[locale.languageCode]!['approve']!;
  String get reject => _localizedValues[locale.languageCode]!['reject']!;
  String get paymentDetailsTitle =>
      _localizedValues[locale.languageCode]!['paymentDetailsTitle']!;
  String get invoiceDetailsTitle =>
      _localizedValues[locale.languageCode]!['invoiceDetailsTitle']!;
  String get invoiceDetails =>
      _localizedValues[locale.languageCode]!['invoiceDetails']!;
  String get editInvoice =>
      _localizedValues[locale.languageCode]!['editInvoice']!;
  String get approveRequestSuccess =>
      _localizedValues[locale.languageCode]!['approveRequestSuccess']!;
  String get rejectRequestSuccess =>
      _localizedValues[locale.languageCode]!['rejectRequestSuccess']!;

  // Helper method to get localized status string
  String getLocalizedStatus(String status) {
    switch (status.toLowerCase()) {
      case 'approved':
        return statusApproved;
      case 'pendingapprover':
        return statusPendingApprover;
      case 'pendingaccountant':
        return statusPendingAccountant;
      case 'fail':
      case 'failed':
        return statusFailed;
      case 'draft':
        return statusDraft;
      case 'cancel':
      case 'cancelled':
        return statusCancelled;
      default:
        return status; // Return original if no mapping found
    }
  }
}
