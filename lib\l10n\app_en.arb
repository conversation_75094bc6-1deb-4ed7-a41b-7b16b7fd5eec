{"personalInfo": "Personal Information", "fullName": "Full Name", "email": "Email", "phoneNumber": "Phone Number", "companyname": "Company Name", "saveChanges": "Save Changes", "updateSuccess": "Information updated successfully", "logout": "Logout", "cancel": "Cancel", "confirmLogout": "Are you sure you want to logout?", "language": "Language", "vietnamese": "Vietnamese", "english": "English", "paymentForm": "Payment Form", "editPayment": "Edit Payment", "amount": "Amount", "currency": "<PERSON><PERSON><PERSON><PERSON>", "status": "Status", "category": "Category", "attachments": "Attachments", "confirmDelete": "Confirm Delete", "delete": "Delete", "edit": "Edit", "searchHint": "Search...", "list": "List", "filterTitle": "Filter", "selectDate": "Select Date", "date": "Date", "clearFilter": "Clear Filter", "applyFilter": "Apply", "filterTooltip": "Filter list", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "statusApproved": "Approved", "statusPendingApprover": "Pending Approval", "statusPendingAccountant": "Pending Accountant", "statusFailed": "Rejected", "statusDraft": "Draft", "statusCancelled": "Cancelled", "notification": "Notification", "unreadNotesTitle": "Unread Note Notifications", "notesCount": "notes", "ok": "OK", "details": "Details", "selectFromGallery": "Select from Gallery", "takeNewPhoto": "Take New Photo", "selectBank": "Select Bank", "errorLoadingProfile": "Unable to load user information", "invalidEmail": "Email must be in @gmail.com format", "pleaseEnterPassword": "Please enter password", "pleaseEnterNewPassword": "Please enter new password", "pleaseConfirmPassword": "Please confirm new password", "passwordMismatch": "Confirmation password does not match", "approveSuccess": "Request approved successfully", "rejectSuccess": "Request rejected successfully", "pleaseEnterReason": "Please enter rejection reason", "invalidOTP": "OTP code is incorrect!", "pleaseCompleteAllFields": "Please fill in all required fields", "markAllAsRead": "<PERSON> as <PERSON>", "idCardInfo": "ID Card Information", "idCard": "ID Card", "issuedDate": "Issued Date", "birthday": "Birthday", "bankAccount": "Bank Account", "accountNumber": "Account Number", "accountHolder": "Account Holder", "noInformation": "No information", "enterField": "Enter", "confirmDeleteBank": "Are you sure you want to delete this bank account?", "deleteBankSuccess": "Bank account deleted successfully", "errorDeletingBank": "Error deleting account", "noBankAccounts": "No bank accounts yet", "deleteAccount": "Delete account", "unsavedData": "Unsaved data", "unsavedChangesMessage": "You have unsaved information. Do you want to leave?", "stayHere": "Stay here", "leave": "Leave", "unsavedChangesExitMessage": "You have unsaved information. Are you sure you want to exit?", "errorLoadingInfo": "Error loading information", "errorUpdatingInfo": "Error updating information", "approvalStatus": "Approval Status", "notifications": "Notifications", "notes": "Notes", "home": "Home", "listView": "List", "pendingList": "Pending List", "profile": "Profile", "loadingPageMessage": "Loading page, please wait...", "newNotification": "New notification", "checkAppForDetails": "Check the app for details", "newNotificationForeground": "New notification (Foreground)", "paymentFormTitle": "Payment Form", "invoiceFormTitle": "Invoice Form", "createNote": "Create Note", "personalProfile": "Personal", "waitingDataLoad": "Please wait for data to finish loading...", "receivedNotificationUpdate": "Received notification update", "unreadNotifications": "unread notifications", "pleaseSelectOrganization": "Please select an organization to login", "pleaseEnterEmail": "Please enter email", "loginFailed": "<PERSON><PERSON> failed", "loginError": "<PERSON><PERSON>", "connectionError": "Connection Error", "cannotConnectToServer": "Cannot connect to server", "organizationNotFound": "Organization not found", "enterEmail": "Enter email", "emailValidationError": "Email must be in valid format (@gmail.com, @yahoo.com, etc.)", "enterPassword": "Enter password", "login": "<PERSON><PERSON>", "cannotLoadUserData": "Cannot load user data", "cannotLoadCurrencyData": "Cannot load currency data", "cannotSelectFile": "Cannot select file. Please try again.", "invalidToken": "Invalid token", "invalidTokenLogin": "Invalid token. Please login again.", "appliedBankInfo": "Applied information of", "beneficiaryName": "Beneficiary Name", "enterBeneficiaryName": "Enter beneficiary name to search...", "searchBankInfoHelper": "Enter name to search saved bank information", "pleaseEnterBeneficiaryName": "Please enter beneficiary name", "connectionTimeout": "Connection too slow. Please try again.", "noServerResponse": "No response from server.", "serverError": "Server error", "requestCancelled": "Request was cancelled.", "cannotConnectServer": "Cannot connect to server.", "unknownError": "An error occurred", "generalError": "Unknown error", "confirm": "Confirm", "unsavedChangesConfirm": "You have unsaved changes. Are you sure you want to exit?", "no": "No", "yes": "Yes", "processingWait": "Processing, please wait a moment...", "enterInformation": "Enter Information", "paymentTitle": "Payment Title", "pleaseEnterPaymentTitle": "Please enter payment title", "pleaseEnterAccountNumber": "Please enter account number", "bank": "Bank", "pleaseEnterBankName": "Please enter bank name", "paymentCurrencyType": "Payment Currency Type", "pleaseSelectCurrencyType": "Please select currency type", "swiftCode": "Swift Code", "pleaseEnterSwiftCode": "Please enter Swift Code", "pleaseEnterAmount": "Please enter amount", "pleaseEnterValidNumber": "Please enter valid number", "paymentContent": "Payment Content", "pleaseEnterPaymentContent": "Please enter payment content", "attachDocuments": "Attach Documents", "selectedFiles": "Selected Files", "success": "Success", "updateRequestSuccess": "Request updated successfully", "createPaymentRequestSuccess": "Payment request created successfully", "pleaseCompleteAllFieldsCorrectly": "Please complete all fields correctly.", "invalidTokenPleaseLogin": "Invalid token. Please login again.", "processingPleaseWait": "Processing, please wait a moment...", "apiErrorCode": "API returned error code", "errorSubmittingForm": "Error submitting form", "buyerName": "Buyer Name", "pleaseEnterBuyerName": "Please enter buyer name", "taxCode": "Tax Code", "pleaseEnterTaxCode": "Please enter tax code", "quantity": "Quantity", "enterQuantity": "Enter quantity", "pleaseEnterQuantity": "Please enter quantity", "quantityMustBeGreaterThanZero": "Quantity must be greater than 0", "unitPrice": "Unit Price", "enterUnitPrice": "Enter unit price", "pleaseEnterUnitPrice": "Please enter unit price", "unitPriceMustBeGreaterThanZero": "Unit price must be greater than 0", "taxRate": "Tax Rate", "pleaseSelectTaxRate": "Please select tax rate", "subtotal": "Subtotal", "totalAmount": "Total Amount", "invoiceContent": "Invoice Content", "enterInvoiceContent": "Enter invoice content", "pleaseEnterInvoiceContent": "Please enter invoice content", "saveDraftSuccess": "Draft saved successfully", "sendRequestSuccess": "Request sent successfully", "selectNotificationDate": "Select notification date (future dates only)", "select": "Select", "invalidDateFormat": "Invalid date format", "invalidDate": "Invalid date", "enterDate": "Enter date", "futureDateOnly": "Only future dates can be selected (from tomorrow onwards)", "close": "Close", "cannotSelectImage": "Cannot select image. Please try again.", "pleaseCompleteRequiredFields": "Please complete all required fields", "userNotFound": "User information not found. Please login again.", "userIdNotFound": "User ID not found. Please login again.", "organizationInfoNotFound": "Organization information not found. Please login again.", "noteSavedSuccess": "Note saved successfully", "errorSavingNote": "Error saving note", "createNoteTitle": "Create Note", "noteInformation": "Note Information", "noteTitleLabel": "Note Title *", "enterNoteTitle": "Enter note title", "pleaseEnterNoteTitle": "Please enter note title", "titleMinLength": "Title must be at least 3 characters", "notificationDateLabel": "Notification Date *", "selectDateFutureOnly": "Select date (future dates only)", "futureDateHelper": "Only dates from tomorrow onwards can be selected", "pleaseSelectNotificationDate": "Please select notification date", "futureDateValidation": "Only future dates can be selected (from tomorrow onwards)", "noteContentLabel": "Note Content *", "enterNoteContent": "Enter detailed note content", "pleaseEnterNoteContent": "Please enter note content", "attachedImages": "Attached Images", "selectImages": "Select Images", "selectedImages": "Selected Images", "saveNote": "Save Note", "errorOccurred": "An error occurred", "tryAgain": "Try Again", "noNotifications": "No notifications", "viewForm": "View Form", "invalidFormType": "Invalid form type", "cannotLoadFormInfo": "Cannot load form information", "storagePermissionRequired": "Storage permission required to download file", "downloadingFile": "Downloading file...", "cannotDownloadFile": "Cannot download file", "cannotOpenFileWeb": "Cannot open file on web", "paymentDetails": "Payment Details", "paymentAmount": "Payment Amount", "downloadFile": "Download file", "download": "Download", "doYouWantToDownload": "Do you want to download", "downloaded": "Downloaded", "pathLabel": "Path", "createdBy": "Created By", "createdAt": "Created At", "updatedAt": "Updated At", "userLabel": "User", "historyLogs": "History Logs", "bankName": "Bank Name", "title": "Title", "description": "Description"}