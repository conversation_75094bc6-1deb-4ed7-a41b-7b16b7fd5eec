import 'package:du_an_flutter/APIconfig/api_config.dart';
import 'package:flutter/material.dart';
import 'package:du_an_flutter/model/activity_item.dart';
import 'package:intl/intl.dart';
import 'package:du_an_flutter/constants/colors.dart';
import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:dio/dio.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:du_an_flutter/services/approval_service.dart';
import 'package:du_an_flutter/l10n/app_localizations.dart';

class TaxDetail extends StatefulWidget {
  final ActivityItem item;

  const TaxDetail({Key? key, required this.item}) : super(key: key);

  @override
  State<TaxDetail> createState() => _TaxDetailState();
}

class _TaxDetailState extends State<TaxDetail> {
  Future<bool> isApprover() async {
    final prefs = await SharedPreferences.getInstance();
    final userRole = prefs.getString('user_role');
    return userRole == 'Approver';
  }

  Future<bool> _requestStoragePermission() async {
    if (Platform.isAndroid) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      if (androidInfo.version.sdkInt <= 32) {
        final status = await Permission.storage.request();
        return status.isGranted;
      } else {
        final photos = await Permission.photos.request();
        final videos = await Permission.videos.request();
        final audio = await Permission.audio.request();
        return photos.isGranted && videos.isGranted && audio.isGranted;
      }
    }
    return true;
  }

  Future<void> _openFile(String url, String fileName) async {
    try {
      print('======== FILE DOWNLOAD DEBUG ========');
      print('Attempting to download file:');
      print('File URL: ${ApiConfig.baseUrl + url}');
      print('File name: $fileName');

      // Store context before async operations
      final localizations = AppLocalizations.of(context);

      final hasPermission = await _requestStoragePermission();
      print('Storage permission granted: $hasPermission');

      if (!hasPermission) {
        throw Exception(localizations.storagePermissionRequired);
      }

      final bool? shouldDownload = await showDialog<bool>(
        context: context,
        builder: (BuildContext context) => AlertDialog(
          title: Text(localizations.downloadFile),
          content: Text('${localizations.doYouWantToDownload} "$fileName"?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: Text(localizations.cancel),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: Text(localizations.download),
            ),
          ],
        ),
      );

      print('User chose to download: $shouldDownload');

      if (shouldDownload == true) {
        String downloadPath;
        if (Platform.isAndroid) {
          final androidInfo = await DeviceInfoPlugin().androidInfo;
          final sdkVersion = androidInfo.version.sdkInt;
          print('Android SDK version: $sdkVersion');

          downloadPath = '/storage/emulated/0/Download';
          print('Using download path: $downloadPath');
        } else {
          downloadPath = '/storage/emulated/0/Download';
          print('Using default download path: $downloadPath');
        }

        final dir = Directory(downloadPath);
        print('Checking if directory exists: ${dir.path}');

        if (!await dir.exists()) {
          print('Directory does not exist, creating it');
          try {
            await dir.create(recursive: true);
            print('Directory created successfully');
          } catch (e) {
            print('Error creating directory: $e');
            throw Exception('Cannot create download directory: $e');
          }
        } else {
          print('Directory exists');
        }

        final sanitizedFileName =
            fileName.replaceAll(RegExp(r'[\\/:*?"<>|]'), '_');
        print('Sanitized file name: $sanitizedFileName');

        final filePath = path.join(dir.path, sanitizedFileName);
        print('Full file path for download: $filePath');

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(localizations.downloadingFile),
              duration: const Duration(seconds: 2),
            ),
          );
        }

        final fullUrl = ApiConfig.baseUrl + url;
        print('Full URL for download: $fullUrl');

        try {
          print('Starting Dio download...');
          await Dio().download(
            fullUrl,
            filePath,
            onReceiveProgress: (received, total) {
              if (total != -1) {
                final percent = (received / total * 100).toStringAsFixed(0);
                print(
                    'Download progress: $received / $total bytes ($percent%)');
              }
            },
          );
          print('Download completed successfully');

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('${localizations.downloaded}: $sanitizedFileName'),
                    const SizedBox(height: 4),
                    Text(
                      '${localizations.pathLabel}: ${dir.path}/$sanitizedFileName',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
                duration: const Duration(seconds: 4),
                action: SnackBarAction(
                  label: localizations.ok,
                  onPressed: () {
                    ScaffoldMessenger.of(context).hideCurrentSnackBar();
                  },
                ),
              ),
            );
          }
        } catch (dioError) {
          print('Dio download error: $dioError');
          throw Exception('Download failed: $dioError');
        }
      }
    } catch (e) {
      print('Error in _openFile: $e');
      print('Error stack trace: ${StackTrace.current}');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                '${AppLocalizations.of(context).cannotDownloadFile}: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      print('======== END FILE DOWNLOAD DEBUG ========');
    }
  }

  Future<void> _openFileInWeb(String url) async {
    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(AppLocalizations.of(context).cannotOpenFileOnWeb)),
        );
      }
    }
  }

  Widget _buildFilePreview(String url) {
    final extension = url.toLowerCase().split('.').last;

    if (['jpg', 'jpeg', 'png'].contains(extension)) {
      return Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: Image.network(
            url,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              print('Error loading image: $error');
              return const Icon(
                Icons.broken_image,
                color: Colors.red,
                size: 24,
              );
            },
          ),
        ),
      );
    }

    return Icon(
      extension == 'pdf'
          ? Icons.picture_as_pdf
          : extension == 'doc' || extension == 'docx'
              ? Icons.description
              : Icons.insert_drive_file,
      color: AppColors.primary,
      size: 40,
    );
  }

  String _formatNumber(String number) {
    String cleanNumber = number.replaceAll(RegExp(r'[,.]'), '');
    try {
      final value = int.parse(cleanNumber);
      final format = NumberFormat('#,###', 'en_US');
      return format.format(value).replaceAll(',', '.');
    } catch (e) {
      return number;
    }
  }

  String _getCurrencySymbol(String currencyCode) {
    const symbols = {
      'VND': '₫',
      'USD': '\$',
      'EUR': '€',
      'GBP': '£',
      'JPY': '¥',
      'CNY': '¥',
      'KRW': '₩',
      'CHF': 'Fr',
    };
    return symbols[currencyCode] ?? currencyCode;
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool>(
      future: isApprover(),
      builder: (context, snapshot) {
        final bool isApproverRole = snapshot.data ?? false;

        return GestureDetector(
          onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
          child: Scaffold(
            backgroundColor: Colors.white,
            appBar: AppBar(
              title: Text(
                '${AppLocalizations.of(context).taxDetailsTitle} - ${widget.item.title}',
                style: const TextStyle(color: Colors.black),
              ),
              backgroundColor: Colors.white,
              elevation: 0,
              iconTheme: const IconThemeData(color: Colors.black),
              scrolledUnderElevation: 0,
              surfaceTintColor: Colors.white,
            ),
            body: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header Section

                        Text(
                          AppLocalizations.of(context).taxDetails,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Title Field
                        _buildDetailField(
                          AppLocalizations.of(context).title,
                          widget.item.title,
                          Icons.title,
                        ),
                        const SizedBox(height: 16),

                        // Tax Type Field

                        // Tax Period Field
                        _buildDetailField(
                          AppLocalizations.of(context).taxPeriod,
                          widget.item.taxPeriod,
                          Icons.date_range,
                        ),
                        const SizedBox(height: 16),

                        // Amount Field
                        _buildAmountField(),
                        const SizedBox(height: 16),

                        // Tax Code Field

                        // Description Field
                        _buildDetailField(
                          AppLocalizations.of(context).description,
                          widget.item.description,
                          Icons.description,
                          maxLines: 3,
                        ),

                        // Attachments Section
                        if (widget.item.attachments != null &&
                            widget.item.attachments!.isNotEmpty) ...[
                          const SizedBox(height: 24),
                          _buildAttachmentsSection(),
                        ],

                        // History Logs Section
                        if (widget.item.historyLogs != null &&
                            widget.item.historyLogs!.isNotEmpty) ...[
                          const SizedBox(height: 24),
                          _buildHistoryLogsSection(),
                        ],
                      ],
                    ),
                  ),
                ),

                // Action Buttons
                if (isApproverRole &&
                    widget.item.status.toLowerCase() == 'pendingapprover') ...[
                  _buildActionButtons(),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'approved':
        return Colors.green;
      case 'pendingapprover':
        return Colors.lightBlue[300]!;
      case 'fail':
        return Colors.red;
      case 'draft':
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }

  Widget _buildDetailField(String label, String value, IconData icon,
      {int maxLines = 1}) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 18, color: AppColors.primary),
              const SizedBox(width: 8),
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value.isNotEmpty
                ? value
                : AppLocalizations.of(context).notAvailable,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
            maxLines: maxLines,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildAmountField() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.green.withOpacity(0.1),
            Colors.green.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.attach_money, size: 18, color: Colors.green),
              const SizedBox(width: 8),
              Text(
                AppLocalizations.of(context).taxAmount,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                _getCurrencySymbol(widget.item.currency),
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _formatNumber(widget.item.price),
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ),
              Text(
                widget.item.currency,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAttachmentsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.attach_file, size: 18, color: Colors.blue),
            const SizedBox(width: 8),
            Text(
              AppLocalizations.of(context).attachments,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${widget.item.attachments!.length}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        ...widget.item.attachments!
            .map((attachment) => _buildAttachmentCard(attachment)),
      ],
    );
  }

  Widget _buildAttachmentCard(dynamic attachment) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 2,
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: _buildFilePreview(ApiConfig.baseUrl + attachment.url),
        title: Text(
          attachment.fileName,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
        ),
        subtitle: Text(
          '${(int.parse(attachment.fileSize) / 1024 / 1024).toStringAsFixed(2)} MB',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        onTap: () => _openFileInWeb(ApiConfig.baseUrl + attachment.url),
        trailing: IconButton(
          icon: const Icon(Icons.download_rounded, color: Colors.blue),
          onPressed: () => _openFile(attachment.url, attachment.fileName),
        ),
      ),
    );
  }

  Widget _buildHistoryLogsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            Icon(Icons.history, size: 18, color: Colors.purple),
            SizedBox(width: 8),
            Text(
              AppLocalizations.of(context).historyLogs,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: DataTable(
            columnSpacing: 20,
            headingRowColor: MaterialStateProperty.all(Colors.grey[100]),
            columns: [
              DataColumn(
                label: Text(
                  AppLocalizations.of(context).createdBy,
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              DataColumn(
                label: Text(
                  AppLocalizations.of(context).description,
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              DataColumn(
                label: Text(
                  AppLocalizations.of(context).createdAt,
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              DataColumn(
                label: Text(
                  AppLocalizations.of(context).updatedAt,
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ],
            rows: widget.item.historyLogs!.map((log) {
              return DataRow(
                cells: [
                  DataCell(Text('User ${log.createdBy}')),
                  DataCell(Text(log.description)),
                  DataCell(Text(
                      log.createdAt.substring(0, 16).replaceAll('T', ' '))),
                  DataCell(Text(
                      log.updatedAt.substring(0, 16).replaceAll('T', ' '))),
                ],
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              icon: const Icon(
                Icons.check_circle,
                color: Colors.white,
                size: 24,
              ),
              label: Text(
                AppLocalizations.of(context).approve,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onPressed: () {
                ApprovalService.showApprovalDialog(
                  context: context,
                  itemId: widget.item.id,
                  onSuccess: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                          content: Text(AppLocalizations.of(context)
                              .approveRequestSuccess)),
                    );
                    Navigator.pop(context, true);
                  },
                );
              },
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton.icon(
              icon: const Icon(
                Icons.cancel,
                color: Colors.white,
                size: 24,
              ),
              label: Text(
                AppLocalizations.of(context).reject,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onPressed: () {
                ApprovalService.showRejectDialog(
                  context: context,
                  itemId: widget.item.id,
                  onSuccess: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                          content: Text(AppLocalizations.of(context)
                              .rejectRequestSuccess)),
                    );
                    Navigator.pop(context, true);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
