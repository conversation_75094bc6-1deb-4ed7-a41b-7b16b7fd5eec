import 'package:du_an_flutter/Screen/invoiceForm.dart';
import 'package:du_an_flutter/Screen/notification_screen.dart';
import 'package:du_an_flutter/Screen/paymentForm.dart';
import 'package:du_an_flutter/Screen/pendingSubmitter.dart';
import 'package:du_an_flutter/Screen/notesListScreen.dart';
import 'package:du_an_flutter/Screen/noteForm.dart';
import 'package:du_an_flutter/Screen/noteDetailScreen.dart';

import 'package:du_an_flutter/Screen/profileScreen.dart';
import 'package:du_an_flutter/constants/colors.dart';
import 'package:du_an_flutter/widgets/loading_overlay.dart';
import 'package:du_an_flutter/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:du_an_flutter/Screen/homeScreen.dart';
import 'package:du_an_flutter/services/notes_service.dart';
import 'package:du_an_flutter/widgets/note_overlay_widget.dart';
import 'package:du_an_flutter/model/note.dart';
import 'dart:async';

class HomePage extends StatefulWidget {
  final int initialTabIndex;

  const HomePage({super.key, this.initialTabIndex = 0});

  @override
  State<HomePage> createState() => _HomePageState();

  // Static method to trigger home refresh from external sources
  static Future<void> triggerHomeRefresh() async {
    return _HomePageState.triggerHomeRefresh();
  }
}

class _HomePageState extends State<HomePage>
    with SingleTickerProviderStateMixin {
  late int _bottomNavIndex;
  OverlayEntry? _overlayEntry;
  bool _isKeyboardVisible = false;
  bool _isLoading = false;
  int _unreadCount = 0; // Số lượng thông báo chưa đọc
  StreamSubscription? _notificationSubscription; // 添加订阅以监听通知更新

  // Thêm GlobalKey cho các màn hình để kiểm tra trạng thái loading
  final homeKey = GlobalKey<HomescreenState>();
  final pendingSubmittersKey = GlobalKey<PendingSubmitterScreenState>();
  final notificationKey = GlobalKey<NotificationScreenState>();
  final noteKey = GlobalKey();
  final profileKey = GlobalKey<ProfilePageState>();

  // Static instance to access from external sources
  static _HomePageState? _instance;

  // Thêm controller cho animation
  late AnimationController _animationController;
  late Animation<double> _animation;

  // Hàm để lấy số lượng thông báo chưa đọc
  void _updateUnreadCount() {
    if (notificationKey.currentState != null) {
      final unreadCount = notificationKey.currentState!.getUnreadCount();
      setState(() {
        _unreadCount = unreadCount;
      });
    }
  }

  // Kiểm tra xem màn hình hiện tại có đang loading không
  bool _isCurrentScreenLoading() {
    try {
      switch (_bottomNavIndex) {
        case 0: // HomeScreen
          return homeKey.currentState?.isLoading ?? false;
        case 1: // PendingApprovalScreen
          return pendingSubmittersKey.currentState?.isLoading ?? false;
        case 2: // NotificationScreen
          return notificationKey.currentState?.isLoading ?? false;
        case 3: // NoteForm
          return false; // Note form doesn't have loading state
        case 4: // ProfileScreen
          return false; // Profile screen doesn't have loading state
        default:
          return false;
      }
    } catch (e) {
      print("Error checking loading state: $e");
      return false;
    }
  }

  @override
  void initState() {
    super.initState();
    // Initialize tab index from widget parameter
    _bottomNavIndex = widget.initialTabIndex;
    // Set static instance for external access
    _instance = this;

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300), // Thời gian animation
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    );

    // 订阅通知更新事件
    _notificationSubscription =
        NotificationCountListener().stream.listen((count) {
      if (mounted) {
        setState(() {
          _unreadCount = count;
          print(
              'Nhận được cập nhật thông báo: $_unreadCount thông báo chưa đọc');
        });
      }
    });

    // 初始化时获取一次未读通知数量
    Future.delayed(const Duration(milliseconds: 500), () {
      _updateUnreadCount();
    });

    // Initialize note overlay system for new app session
    try {
      NotesService.resetOverlayFlag();

      // Schedule note overlay check when user reaches home screen after login
      _scheduleNoteOverlayCheck();
    } catch (e) {
      print('Error initializing note overlay system: $e');
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Cập nhật số lượng thông báo khi dependencies thay đổi
    _updateUnreadCount();
  }

  @override
  void dispose() {
    // Clear static instance
    if (_instance == this) {
      _instance = null;
    }
    _animationController.dispose();
    _notificationSubscription?.cancel(); // 取消订阅
    super.dispose();
  }

  // Static method to trigger home refresh from external sources
  static Future<void> triggerHomeRefresh() async {
    print('HomePage - triggerHomeRefresh called');
    if (_instance != null && _instance!.mounted) {
      // If currently on home tab (index 0), refresh immediately
      if (_instance!._bottomNavIndex == 0) {
        print('HomePage - Currently on home tab, refreshing immediately');
        await _instance!.homeKey.currentState?.forceRefresh();
      } else {
        print(
            'HomePage - Not on home tab, will refresh when user switches to home tab');
        // The refresh will happen automatically when user switches to home tab
        // due to the existing activate() method in homeScreen.dart
      }
    } else {
      print('HomePage - Instance not available or not mounted');
    }
  }

  // Schedule note overlay check with 1000ms delay
  void _scheduleNoteOverlayCheck() {
    try {
      Timer(const Duration(milliseconds: 1000), () {
        if (mounted && _bottomNavIndex == 0) {
          // Only show overlay when user is on home tab
          _checkAndShowNoteOverlay();
        }
      });
    } catch (e) {
      print('Error scheduling note overlay check: $e');
    }
  }

  // Check for unread notes and show overlay if needed
  Future<void> _checkAndShowNoteOverlay() async {
    try {
      // First check if there's a temporarily dismissed overlay
      if (NotesService.hasTemporarilyDismissedOverlay()) {
        final dismissedNotes = NotesService.getTemporarilyDismissedNotes();
        if (dismissedNotes != null && dismissedNotes.isNotEmpty && mounted) {
          // Clear the temporarily dismissed flag and show the overlay again
          NotesService.clearTemporarilyDismissedOverlay();
          _showNoteOverlay(dismissedNotes);
          return;
        }
      }

      // Get all unread notes (not just today's notes)
      final unreadNotes = await NotesService.getUnreadNotes();

      if (unreadNotes.isNotEmpty && mounted) {
        // Show the overlay with unread notes
        _showNoteOverlay(unreadNotes);
      }
    } catch (e) {
      // Silently handle errors to avoid breaking the home screen
      print('Error checking note overlay: $e');
    }
  }

  // Show note overlay
  void _showNoteOverlay(List<Note> notes) {
    try {
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false, // Mandatory reading for today's content
          builder: (context) => NoteOverlayWidget(
            notes: notes,
            onClose: () {
              Navigator.of(context).pop();
            },
            onTemporaryDismiss: () {
              Navigator.of(context).pop();
            },
            onNoteDetail: (note) {
              // Close overlay first
              Navigator.of(context).pop();
              // Navigate to note detail
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => NoteDetailScreen(note: note),
                ),
              );
            },
          ),
        );
      }
    } catch (e) {
      print('Error showing note overlay: $e');
    }
  }

  void _showOverlay(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isPortrait =
        MediaQuery.of(context).orientation == Orientation.portrait;

    // Sử dụng tỷ lệ phần trăm cho responsive design
    // FAB position percentages (based on Flutter's default FAB positioning)
    final fabMarginPercent = 16.0 / size.width; // ~2.3% trên màn hình 375px
    final fabTransformOffsetPercent =
        10.0 / size.height; // ~1.3% trên màn hình 812px
    final bottomNavHeightPercent =
        (kBottomNavigationBarHeight + 20) / size.height; // ~10.8%

    // Calculate FAB center position using percentages
    final fabSizePercent = 56.0 / size.width; // ~14.9% width
    final fabCenterXPercent =
        1.0 - fabMarginPercent - (fabSizePercent / 2); // ~95.5%
    final fabCenterYPercent = 1.0 -
        bottomNavHeightPercent -
        fabMarginPercent -
        (56.0 / size.height / 2) +
        fabTransformOffsetPercent;

    // fabCenterX và fabCenterY không cần thiết vì chúng ta sử dụng percentages

    // Calculate popup dimensions using responsive percentages
    final popupWidthPercent = isPortrait
        ? 0.6
        : 0.35; // 60% portrait, 35% landscape (giảm từ 75%/45%)
    final popupWidth = size.width * popupWidthPercent;

    // Popup height as percentage of screen height
    final popupHeightPercent = 0.28; // ~28% of screen height

    // Calculate popup position using percentages
    final popupLeftPercent = fabCenterXPercent -
        (popupWidthPercent / 2) +
        0.08; // Dịch popup sang phải 8% để thấy rõ hơn
    final popupBottomPercent = 1.0 -
        fabCenterYPercent +
        (56.0 / size.height / 2) -
        0.04; // Popup nằm trên FAB (overlap 2% chiều cao màn hình)

    // Safe margins as percentage (2% of screen) - giảm để cho phép popup di chuyển
    final minMarginPercent = 0.02; // 2% margin

    final safePopupLeftPercent = popupLeftPercent.clamp(
        minMarginPercent, 1.0 - popupWidthPercent - minMarginPercent);
    final safePopupBottomPercent = popupBottomPercent.clamp(
        minMarginPercent, 1.0 - popupHeightPercent - minMarginPercent);

    // Convert final percentages to pixels
    final safePopupLeft = size.width * safePopupLeftPercent;
    final safePopupBottom = size.height * safePopupBottomPercent;

    // Calculate arrow position relative to popup (percentage-based)
    final arrowLeftPercent = fabCenterXPercent -
        safePopupLeftPercent -
        (8.0 / size.width); // 8px is half arrow width
    final arrowLeft = size.width * arrowLeftPercent;

    _overlayEntry = OverlayEntry(
      builder: (context) => AnimatedBuilder(
        animation: _animation,
        builder: (context, child) => Stack(
          children: [
            // Background với fade animation
            GestureDetector(
              onTap: _removeOverlay,
              child: Container(
                color: Colors.black.withOpacity(0.5 * _animation.value),
                width: double.infinity,
                height: double.infinity,
              ),
            ),
            // Popup với slide và fade animation positioned above FAB
            Positioned(
              bottom: safePopupBottom,
              left: safePopupLeft,
              width: popupWidth,
              child: Transform.translate(
                offset: Offset(
                    0, 30 * (1 - _animation.value)), // Reduced slide distance
                child: Opacity(
                  opacity: _animation.value,
                  child: Material(
                    color: Colors.transparent,
                    child: _buildIntegratedPopup(
                      popupWidth: popupWidth,
                      arrowLeft: arrowLeft,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
    _animationController.forward(); // Bắt đầu animation khi hiển thị
  }

  void _removeOverlay() {
    _animationController.reverse().then((_) {
      _overlayEntry?.remove();
      _overlayEntry = null;
    });
  }

  Widget _buildPopupItem({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    bool isFirst = false,
    bool isLast = false,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(
          vertical: 3, horizontal: 6), // Giảm margin để compact hơn
      decoration: BoxDecoration(
        color: Colors.white
            .withOpacity(0.95), // Nền trắng bán trong suốt cho mỗi nút
        borderRadius: BorderRadius.circular(12), // Bo góc riêng cho từng nút
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
            spreadRadius: 1,
          ),
        ],
        border: Border.all(
          color: Colors.grey.withOpacity(0.2),
          width: 0.5,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.symmetric(
                vertical: 12, horizontal: 14), // Giảm padding để compact hơn
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.15),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: AppColors.primary, size: 18),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    label,
                    style: const TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 14,
                  color: Colors.grey[500],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Không cần divider nữa vì các nút đã tách biệt

  Widget _buildIntegratedPopup({
    required double popupWidth,
    required double arrowLeft,
  }) {
    // Calculate arrow position as percentage (0.0 to 1.0) of popup width
    final arrowPosition =
        (arrowLeft + 8) / popupWidth; // +8 for half arrow width
    final clampedArrowPosition =
        arrowPosition.clamp(0.1, 0.9); // Keep arrow within bounds

    // Calculate responsive popup height based on screen size
    final screenHeight = MediaQuery.of(context).size.height;
    final popupHeight = screenHeight * 0.28; // 28% of screen height

    return Stack(
      children: [
        // Shadow layer
        CustomPaint(
          size: Size(
              popupWidth,
              popupHeight +
                  10), // popup height + arrow height (responsive height)
          painter: PopupShadowPainter(
            arrowPosition: clampedArrowPosition,
            arrowWidth: 20.0,
            arrowHeight: 10.0,
            borderRadius: 16.0,
            shadowColor: Colors.black.withOpacity(0.15),
            shadowBlur: 20.0,
            shadowOffset: const Offset(0, 8),
          ),
        ),
        // Main popup with integrated arrow
        ClipPath(
          clipper: PopupWithArrowClipper(
            arrowPosition: clampedArrowPosition,
            arrowWidth: 20.0,
            arrowHeight: 10.0,
            borderRadius: 16.0,
          ),
          child: Container(
            width: popupWidth,
            color: Colors.transparent, // Nền trong suốt cho container chính
            padding:
                const EdgeInsets.symmetric(vertical: 8), // Padding cho các nút
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildPopupItem(
                  icon: Icons.description,
                  label: 'Mẫu thanh toán',
                  onTap: () {
                    _removeOverlay();
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => const PaymentForm()),
                    );
                  },
                  isFirst: true,
                ),
                _buildPopupItem(
                  icon: Icons.article,
                  label: 'Mẫu hóa đơn',
                  onTap: () {
                    _removeOverlay();
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => const InvoiceForm()),
                    );
                  },
                ),
                _buildPopupItem(
                  icon: Icons.note_add,
                  label: 'Tạo ghi chú',
                  onTap: () {
                    _removeOverlay();
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => const NoteForm()),
                    );
                  },
                  isLast: true,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // List of icons for the bottom nav bar
  final List<IconData> iconList = [
    Icons.home,
    Icons.list_alt,
    Icons.notifications,
    Icons.note_add,
    Icons.person,
  ];

  // List of pages
  late final List<Widget> _pages = [
    Homescreen(key: homeKey),
    PendingSubmitterScreen(key: pendingSubmittersKey),
    NotificationScreen(key: notificationKey),
    NotesListScreen(key: noteKey),
    ProfilePage(key: profileKey),
  ];

  // Chuyển tab có hiển thị loading
  Future<void> _switchTabWithLoading(int newIndex) async {
    if (_isLoading) return;

    // Đảm bảo không có focus trước khi chuyển tab
    FocusManager.instance.primaryFocus?.unfocus();

    // Chuyển tab ngay lập tức để có phản hồi tức thì
    setState(() {
      _bottomNavIndex = newIndex;
      _isLoading = true;
    });

    try {
      // Hiển thị loading trong khoảng thời gian ngắn hơn để tạo hiệu ứng chuyển trang
      await Future.delayed(const Duration(milliseconds: 300));

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }

      // Nếu chuyển sang tab thông báo, cập nhật số lượng thông báo chưa đọc
      if (newIndex == 2) {
        _updateUnreadCount();
      }

      // Nếu chuyển từ tab thông báo sang tab khác, cũng cập nhật số lượng
      if (_bottomNavIndex == 2 && newIndex != 2) {
        _updateUnreadCount();
      }
    } catch (e) {
      print("Lỗi khi chuyển tab: $e");
      if (mounted) {
        setState(() => _isLoading = false);
      }
    } finally {
      // Đảm bảo luôn đặt _isLoading về false sau 350ms kể cả khi có lỗi
      Future.delayed(const Duration(milliseconds: 350), () {
        if (mounted && _isLoading) {
          setState(() => _isLoading = false);
          // Đảm bảo không có focus sau khi loading hoàn tất
          FocusManager.instance.primaryFocus?.unfocus();
        }
      });
    }

    // Force refresh of PendingApprovals when switching to that tab
    if (newIndex == 1 && pendingSubmittersKey.currentState != null) {
      pendingSubmittersKey.currentState!.forceReload();
    }

    // Force refresh of Home screen when switching to that tab
    if (newIndex == 0 && homeKey.currentState != null) {
      // Use a small delay to ensure the tab switch animation completes
      Future.delayed(const Duration(milliseconds: 100), () {
        homeKey.currentState!.forceRefresh();
      });

      // Check for temporarily dismissed overlay on any tab switch
      try {
        Future.delayed(const Duration(milliseconds: 1200), () {
          if (mounted) {
            // Check for temporarily dismissed overlay on any tab
            if (NotesService.hasTemporarilyDismissedOverlay()) {
              final dismissedNotes =
                  NotesService.getTemporarilyDismissedNotes();
              if (dismissedNotes != null && dismissedNotes.isNotEmpty) {
                // Clear the temporarily dismissed flag and show the overlay again
                NotesService.clearTemporarilyDismissedOverlay();
                _showNoteOverlay(dismissedNotes);
                return;
              }
            }

            // Only check for new unread notes when switching to home tab
            if (_bottomNavIndex == 0) {
              _checkAndShowNoteOverlay();
            }
          }
        });
      } catch (e) {
        print('Error scheduling note overlay check on tab switch: $e');
      }
    }

    // Note tab doesn't need refresh as it's a form
  }

  @override
  Widget build(BuildContext context) {
    // Kiểm tra bàn phím có đang hiển thị không
    _isKeyboardVisible = MediaQuery.of(context).viewInsets.bottom > 0;

    return Scaffold(
      // Sử dụng Stack để hiển thị loading overlay
      body: Stack(
        children: [
          // IndexedStack để giữ lại trạng thái của các màn hình
          IndexedStack(
            index: _bottomNavIndex,
            children: _pages,
          ),
          // Hiển thị loading overlay khi đang tải
          if (_isLoading) const LoadingOverlay()
        ],
      ),
      // Show FAB on all tabs except Profile tab (index 4) and when keyboard is not visible
      floatingActionButton: (_isKeyboardVisible || _bottomNavIndex == 4)
          ? null
          : Transform.translate(
              offset: const Offset(0, -10),
              child: FloatingActionButton(
                shape: const CircleBorder(),
                onPressed: _isLoading
                    ? null
                    : () {
                        _showOverlay(context);
                      },
                backgroundColor: AppColors.primary,
                child: const Icon(Icons.add, color: Colors.white),
              ),
            ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.2),
              spreadRadius: 1,
              blurRadius: 8,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          child: BottomNavigationBar(
            items: <BottomNavigationBarItem>[
              BottomNavigationBarItem(
                icon: Icon(Icons.home_outlined),
                activeIcon: Icon(Icons.home),
                label: AppLocalizations.of(context).home,
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.list_alt_outlined),
                activeIcon: Icon(Icons.list_alt),
                label: AppLocalizations.of(context).listView,
              ),
              BottomNavigationBarItem(
                icon: Badge(
                  label: Text(
                    _unreadCount.toString(),
                    style: TextStyle(color: Colors.white, fontSize: 10),
                  ),
                  child: Icon(Icons.notifications_outlined),
                ),
                activeIcon: Badge(
                  label: Text(
                    _unreadCount.toString(),
                    style: TextStyle(color: Colors.white, fontSize: 10),
                  ),
                  child: Icon(Icons.notifications),
                ),
                label: AppLocalizations.of(context).notifications,
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.note_add_outlined),
                activeIcon: Icon(Icons.note_add),
                label: AppLocalizations.of(context).notes,
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.person_outline),
                activeIcon: Icon(Icons.person),
                label: AppLocalizations.of(context).p,
              ),
            ],
            currentIndex: _bottomNavIndex,
            selectedItemColor: AppColors.primary,
            unselectedItemColor: Colors.grey,
            backgroundColor: Colors.white,
            type: BottomNavigationBarType.fixed,
            selectedLabelStyle:
                TextStyle(color: Colors.grey[800], fontSize: 12),
            unselectedLabelStyle:
                TextStyle(color: Colors.grey[400], fontSize: 12),
            elevation: 0,
            onTap: (index) async {
              if (_isLoading) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Đang chuyển trang, vui lòng đợi...'),
                    backgroundColor: Colors.orange,
                    duration: Duration(seconds: 1),
                  ),
                );
                return;
              }

              if (_isCurrentScreenLoading()) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Vui lòng đợi dữ liệu đang tải xong...'),
                    backgroundColor: Colors.orange,
                    duration: Duration(seconds: 2),
                  ),
                );
                return;
              }

              if (_bottomNavIndex == 4 && index != 4) {
                final profileState = profileKey.currentState;
                if (profileState != null && profileState.isEdited) {
                  final canLeave = await profileState.handleTabNavigation();
                  if (!canLeave) {
                    return;
                  }
                }
              }
              _switchTabWithLoading(index);
            },
          ),
        ),
      ),
    );
  }
}

// Custom clipper for popup with integrated arrow
class PopupWithArrowClipper extends CustomClipper<Path> {
  final double arrowPosition; // Position of arrow from left edge (0.0 to 1.0)
  final double arrowWidth;
  final double arrowHeight;
  final double borderRadius;

  PopupWithArrowClipper({
    required this.arrowPosition,
    this.arrowWidth = 20.0,
    this.arrowHeight = 10.0,
    this.borderRadius = 16.0,
  });

  @override
  Path getClip(Size size) {
    final path = Path();
    final arrowStartX = (size.width * arrowPosition) - (arrowWidth / 2);
    final arrowEndX = arrowStartX + arrowWidth;
    final arrowTipX = arrowStartX + (arrowWidth / 2);

    // Start from top-left corner
    path.moveTo(borderRadius, 0);

    // Top edge with top-right corner
    path.lineTo(size.width - borderRadius, 0);
    path.arcToPoint(
      Offset(size.width, borderRadius),
      radius: Radius.circular(borderRadius),
    );

    // Right edge with bottom-right corner
    path.lineTo(size.width, size.height - borderRadius);
    path.arcToPoint(
      Offset(size.width - borderRadius, size.height),
      radius: Radius.circular(borderRadius),
    );

    // Bottom edge - right side of arrow
    path.lineTo(arrowEndX, size.height);

    // Arrow pointing down
    path.lineTo(arrowTipX, size.height + arrowHeight);
    path.lineTo(arrowStartX, size.height);

    // Bottom edge - left side of arrow, continuing to bottom-left
    path.lineTo(borderRadius, size.height);
    path.arcToPoint(
      Offset(0, size.height - borderRadius),
      radius: Radius.circular(borderRadius),
    );

    // Left edge with top-left corner
    path.lineTo(0, borderRadius);
    path.arcToPoint(
      Offset(borderRadius, 0),
      radius: Radius.circular(borderRadius),
    );

    path.close();
    return path;
  }

  @override
  bool shouldReclip(PopupWithArrowClipper oldClipper) {
    return oldClipper.arrowPosition != arrowPosition ||
        oldClipper.arrowWidth != arrowWidth ||
        oldClipper.arrowHeight != arrowHeight ||
        oldClipper.borderRadius != borderRadius;
  }
}

// Custom painter for popup shadow with integrated arrow
class PopupShadowPainter extends CustomPainter {
  final double arrowPosition; // Position of arrow from left edge (0.0 to 1.0)
  final double arrowWidth;
  final double arrowHeight;
  final double borderRadius;
  final Color shadowColor;
  final double shadowBlur;
  final Offset shadowOffset;

  PopupShadowPainter({
    required this.arrowPosition,
    this.arrowWidth = 20.0,
    this.arrowHeight = 10.0,
    this.borderRadius = 16.0,
    this.shadowColor = const Color(0x26000000), // 15% black
    this.shadowBlur = 20.0,
    this.shadowOffset = const Offset(0, 8),
  });

  @override
  void paint(Canvas canvas, Size size) {
    final path = Path();
    final arrowStartX = (size.width * arrowPosition) - (arrowWidth / 2);
    final arrowEndX = arrowStartX + arrowWidth;
    final arrowTipX = arrowStartX + (arrowWidth / 2);

    // Create the same path as the clipper
    path.moveTo(borderRadius, 0);
    path.lineTo(size.width - borderRadius, 0);
    path.arcToPoint(
      Offset(size.width, borderRadius),
      radius: Radius.circular(borderRadius),
    );
    path.lineTo(size.width, size.height - borderRadius);
    path.arcToPoint(
      Offset(size.width - borderRadius, size.height),
      radius: Radius.circular(borderRadius),
    );
    path.lineTo(arrowEndX, size.height);
    path.lineTo(arrowTipX, size.height + arrowHeight);
    path.lineTo(arrowStartX, size.height);
    path.lineTo(borderRadius, size.height);
    path.arcToPoint(
      Offset(0, size.height - borderRadius),
      radius: Radius.circular(borderRadius),
    );
    path.lineTo(0, borderRadius);
    path.arcToPoint(
      Offset(borderRadius, 0),
      radius: Radius.circular(borderRadius),
    );
    path.close();

    // Draw shadow
    final shadowPaint = Paint()
      ..color = shadowColor
      ..style = PaintingStyle.fill
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, shadowBlur);

    canvas.drawPath(path.shift(shadowOffset), shadowPaint);
  }

  @override
  bool shouldRepaint(PopupShadowPainter oldDelegate) {
    return oldDelegate.arrowPosition != arrowPosition ||
        oldDelegate.arrowWidth != arrowWidth ||
        oldDelegate.arrowHeight != arrowHeight ||
        oldDelegate.borderRadius != borderRadius ||
        oldDelegate.shadowColor != shadowColor ||
        oldDelegate.shadowBlur != shadowBlur ||
        oldDelegate.shadowOffset != shadowOffset;
  }
}
