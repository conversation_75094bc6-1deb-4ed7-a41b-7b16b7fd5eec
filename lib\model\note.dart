class Note {
  final int id;
  final String title;
  final String description;
  final String notiDate;
  final String? image;
  final bool isRead;

  Note({
    required this.id,
    required this.title,
    required this.description,
    required this.notiDate,
    this.image,
    required this.isRead,
  });

  factory Note.fromJson(Map<String, dynamic> json) {
    return Note(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      notiDate: json['notiDate'] ?? '',
      image: json['image'],
      isRead: json['isRead'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'notiDate': notiDate,
      'image': image,
      'isRead': isRead,
    };
  }
}
