import 'dart:async';
import 'package:du_an_flutter/constants/colors.dart';
import 'package:du_an_flutter/model/notification_item.dart';
import 'package:du_an_flutter/services/notification_service.dart';
import 'package:du_an_flutter/widgets/loading_overlay.dart';
import 'package:flutter/material.dart';
import 'package:du_an_flutter/L10n/l10n.dart';
import 'package:du_an_flutter/l10n/app_localizations.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:du_an_flutter/Screen/invoiceForm.dart';
import 'package:du_an_flutter/Screen/paymentForm.dart';
import 'package:du_an_flutter/Screen/payment_detail.dart';
import 'package:du_an_flutter/Screen/invoice_detail.dart';

// 通知监听器类 - 用于在通知状态变化时发送事件
class NotificationCountListener {
  static final NotificationCountListener _instance =
      NotificationCountListener._internal();

  factory NotificationCountListener() => _instance;

  NotificationCountListener._internal();

  final _controller = StreamController<int>.broadcast();

  Stream<int> get stream => _controller.stream;

  void updateUnreadCount(int count) {
    _controller.add(count);
  }

  void dispose() {
    _controller.close();
  }
}

// Export the state type for external use
typedef NotificationScreenState = _NotificationScreenState;

class NotificationScreen extends StatefulWidget {
  const NotificationScreen({super.key});

  @override
  State<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen>
    with WidgetsBindingObserver {
  // Add a getter to expose loading state to parent widgets
  bool get isLoading => _isLoading;
  bool _isLoading = true;
  List<NotificationItem> _notifications = [];
  String _errorMessage = '';

  // Timer để polling notifications
  Timer? _refreshTimer;

  // Stream subscription cho Firebase message
  StreamSubscription? _firebaseMessageSubscription;

  // 获取未读通知数量
  int getUnreadCount() {
    // 如果正在加载，返回0或上次的数量
    if (_isLoading) return 0;

    final count =
        _notifications.where((notification) => notification.isRead == 0).length;

    NotificationCountListener().updateUnreadCount(count);

    return count;
  }

  @override
  void initState() {
    super.initState();
    // Đăng ký observer cho lifecycle events
    WidgetsBinding.instance.addObserver(this);

    // Load notifications khi mở màn hình
    _loadNotifications();

    // Bắt đầu polling định kỳ (30 giây)
    _startPeriodicRefresh();

    // Đăng ký lắng nghe tin nhắn Firebase
    _setupFirebaseMessaging();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // Khi app quay lại foreground, refresh notifications
    if (state == AppLifecycleState.resumed) {
      print('App resumed - reloading notifications');
      _loadNotifications();
    }
  }

  void _startPeriodicRefresh() {
    // Hủy timer cũ nếu tồn tại
    _refreshTimer?.cancel();

    // Tạo timer mới, refresh mỗi 30 giây
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (mounted) {
        print('Periodic refresh - reloading notifications');
        _loadNotifications(silent: true);
      }
    });
  }

  Future<void> _setupFirebaseMessaging() async {
    try {
      // Lắng nghe tin nhắn foreground
      _firebaseMessageSubscription =
          FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        print('Received foreground message: ${message.notification?.title}');

        // Nếu là notification, refresh danh sách
        if (message.notification != null) {
          if (mounted) {
            print('Notification received - reloading notifications');
            _loadNotifications(silent: true);
          }
        }
      });

      // Đăng ký lắng nghe khi mở app từ terminated state
      FirebaseMessaging.instance.getInitialMessage().then((message) {
        if (message != null && mounted) {
          print(
              'App opened from terminated state by notification - reloading notifications');
          _loadNotifications();
        }
      });

      // Đăng ký lắng nghe khi mở app từ background
      FirebaseMessaging.onMessageOpenedApp.listen((message) {
        if (mounted) {
          print(
              'App opened from background by notification - reloading notifications');
          _loadNotifications();
        }
      });
    } catch (e) {
      print('Error setting up Firebase Messaging: $e');
    }
  }

  @override
  void dispose() {
    // Hủy đăng ký observer
    WidgetsBinding.instance.removeObserver(this);

    // Hủy timer
    _refreshTimer?.cancel();
    _refreshTimer = null;

    // Hủy subscription Firebase
    _firebaseMessageSubscription?.cancel();
    _firebaseMessageSubscription = null;

    super.dispose();
  }

  Future<void> _loadNotifications({bool silent = false}) async {
    if (!mounted) return;

    // Chỉ hiển thị loading nếu không ở chế độ silent
    if (!silent) {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });
    }

    try {
      final notifications = await NotificationService.getNotifications();
      if (!mounted) return;

      setState(() {
        _notifications = notifications;
        _isLoading = false;
      });

      // 更新未读通知数并广播
      final unreadCount = getUnreadCount();
      NotificationCountListener().updateUnreadCount(unreadCount);

      print(
          'Loaded ${notifications.length} notifications, unread: $unreadCount');
    } catch (e) {
      if (!mounted) return;

      print('Error loading notifications: $e');
      setState(() {
        if (!silent) {
          _errorMessage = e.toString();
        }
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        scrolledUnderElevation: 0,
        surfaceTintColor: Colors.white,
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          l10n.notification,
          style: TextStyle(
            color: Colors.black,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1.0),
          child: Container(
            color: Colors.grey.shade600,
            height: 1.0,
          ),
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingOverlay();
    }

    if (_errorMessage.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'Đã xảy ra lỗi',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _errorMessage,
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey[600]),
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadNotifications,
              child: const Text('Thử lại'),
            ),
          ],
        ),
      );
    }

    if (_notifications.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.notifications_off_outlined,
              size: 72,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Không có thông báo',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadNotifications,
      color: AppColors.primary,
      child: ListView.separated(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
        itemCount: _notifications.length,
        separatorBuilder: (context, index) => const Divider(height: 1),
        itemBuilder: (context, index) {
          final notification = _notifications[index];
          return _buildNotificationItem(notification);
        },
      ),
    );
  }

  Widget _buildNotificationItem(NotificationItem notification) {
    return InkWell(
      onTap: () {
        // Hiển thị popup chi tiết khi nhấn vào thông báo
        _handleNotificationTap(notification);
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
        decoration: BoxDecoration(
          color: notification.read ? Colors.white : const Color(0xFFE3F2FD),
          border: Border(
            bottom: BorderSide(color: Colors.grey.shade300, width: 1),
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: const EdgeInsets.only(right: 16, top: 2),
              child: Icon(
                notification.read
                    ? Icons.notifications_outlined
                    : Icons.notifications,
                color: const Color(0xFF30C1BC),
                size: 30,
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    notification.title,
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 16,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    notification.body,
                    style: const TextStyle(
                      color: Colors.black54,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    notification.formattedDate,
                    style: const TextStyle(
                      color: Colors.black45,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 标记通知为已读
  Future<void> _markNotificationAsRead(NotificationItem notification) async {
    if (notification.isRead == 1) return; // 已经是已读状态

    try {
      final success = await NotificationService.markAsRead(notification.id);
      if (success && mounted) {
        setState(() {
          // 在本地更新通知状态
          final index = _notifications.indexOf(notification);
          if (index != -1) {
            // 创建一个新的通知对象，并设置为已读
            final updatedNotification = NotificationItem(
              id: notification.id,
              userId: notification.userId,
              title: notification.title,
              body: notification.body,
              type: notification.type,
              refId: notification.refId,
              isRead: true, // 设置为已读
              createdAt: notification.createdAt,
              updatedAt: notification.updatedAt,
              deletedAt: notification.deletedAt,
            );

            // 更新列表
            _notifications[index] = updatedNotification;
          }
        });

        // 更新未读数量并广播
        final unreadCount = getUnreadCount();
        NotificationCountListener().updateUnreadCount(unreadCount);
      }
    } catch (e) {
      print('Error marking notification as read: $e');
    }
  }

  // 处理通知点击
  Future<void> _handleNotificationTap(NotificationItem notification) async {
    // 标记为已读
    await _markNotificationAsRead(notification);

    // Hiển thị dialog chi tiết thông báo
    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Icon chuông
              Icon(
                Icons.notifications,
                size: 48,
                color: Colors.black,
              ),
              const SizedBox(height: 24),
              // Tiêu đề
              Text(
                notification.title,
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 20,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              // Nội dung
              Text(
                notification.body,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.black54,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              // Nút xem form
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF30C1BC),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  onPressed: () async {
                    try {
                      print('=== NOTIFICATION TAP DEBUG ===');
                      print('Notification ID: ${notification.id}');
                      print('Notification RefID: ${notification.refId}');
                      print('Notification Type: ${notification.type}');

                      // Gọi API để lấy thông tin form
                      final formData = await NotificationService.getFormById(
                          notification.refId);

                      print('Successfully retrieved form data:');
                      print('Form ID: ${formData.id}');
                      print('Form Title: ${formData.title}');
                      print('Form Status: ${formData.status}');
                      print('Category Name: ${formData.category.name}');

                      // Đóng dialog hiện tại
                      Navigator.pop(context);

                      // Điều hướng dựa vào loại category
                      if (formData.category.id == 1) {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => PaymentDetail(
                              item: formData,
                            ),
                          ),
                        );
                      } else if (formData.category.id == 2) {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => InvoiceDetail(
                              item: formData,
                            ),
                          ),
                        );
                      } else {
                        // Hiển thị thông báo nếu category không hợp lệ
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                                'Loại form không hợp lệ: ${formData.category.name}'),
                            backgroundColor: Colors.orange,
                          ),
                        );
                        return;
                      }
                    } catch (e) {
                      print('Error in notification tap handler: $e');
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                              'Không thể tải thông tin form: ${e.toString()}'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  },
                  child: const Text(
                    'Xem form',
                    style: TextStyle(fontSize: 16),
                  ),
                ),
              ),
              const SizedBox(height: 12),
              // Nút đóng
              SizedBox(
                width: double.infinity,
                child: OutlinedButton(
                  style: OutlinedButton.styleFrom(
                    side: const BorderSide(color: Color(0xFF30C1BC)),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  onPressed: () {
                    // Đóng dialog
                    Navigator.pop(context);
                  },
                  child: const Text(
                    'Đóng',
                    style: TextStyle(
                      fontSize: 16,
                      color: Color(0xFF30C1BC),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
