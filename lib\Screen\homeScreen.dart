import 'dart:async';
import 'package:du_an_flutter/APIconfig/api_endpoints.dart';
import 'package:du_an_flutter/APIconfig/api_config.dart';
import 'package:du_an_flutter/widgets/loading_overlay.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dio/dio.dart';
import 'package:flutter/services.dart';

import 'package:du_an_flutter/Screen/activities_screen.dart';
import 'package:du_an_flutter/constants/colors.dart';
import 'package:du_an_flutter/model/activity_item.dart';
import 'package:flutter/material.dart';
//import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:du_an_flutter/l10n/app_localizations.dart';
import 'package:du_an_flutter/services/api_service.dart';
// Note overlay imports removed - handled in pageHome.dart and pageHomeApprover.dart

// Export the state for external use
typedef HomescreenState = _HomescreenState;

class Homescreen extends StatefulWidget {
  const Homescreen({super.key});

  @override
  State<Homescreen> createState() => _HomescreenState();
}

class _HomescreenState extends State<Homescreen> with WidgetsBindingObserver {
  List<ActivityItem> items = [];
  List<ActivityItem> filteredItems = [];

  // Add a getter to expose loading state to parent widgets
  bool get isLoading => _isLoading || isLoadingMore || isSearching;
  bool _isLoading = true;

  bool isLoadingMore = false;
  bool isSearching = false;
  int currentPage = 1;
  final int itemsPerPage = 6;
  bool hasMoreItems = true;
  String? statusFilter;
  DateTime? dateFilter;
  int? categoryFilter;
  final TextEditingController _searchController = TextEditingController();
  Timer? _debounce;
  String _searchQuery = '';

  // Thêm ScrollController để theo dõi scroll
  final ScrollController _scrollController = ScrollController();
  bool isApprover = false;

  // Thêm FocusNode để quản lý focus của TextField
  final FocusNode _searchFocusNode = FocusNode();
  // Thêm biến để theo dõi trạng thái bàn phím
  bool _isKeyboardVisible = false;

  @override
  void initState() {
    super.initState();
    // Đăng ký observer để theo dõi trạng thái ứng dụng
    WidgetsBinding.instance.addObserver(this);
    _scrollController.addListener(_scrollListener);
    _loadActivities();
    _checkRole();

    // Note: Overlay logic moved to pageHome.dart and pageHomeApprover.dart to avoid duplicates
  }

  @override
  void dispose() {
    // Hủy đăng ký observer khi dispose
    WidgetsBinding.instance.removeObserver(this);
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _searchController.dispose();
    _searchFocusNode.dispose(); // Dispose FocusNode khi widget dispose
    _debounce?.cancel();
    super.dispose();
  }

  // Theo dõi khi bàn phím hiện/ẩn để unfocus search field
  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    final bottomInset = WidgetsBinding.instance.window.viewInsets.bottom;
    final newValue = bottomInset > 0.0;

    // Nếu bàn phím vừa ẩn, unfocus search field
    if (_isKeyboardVisible && !newValue) {
      _unfocusSearch();
    }

    _isKeyboardVisible = newValue;
  }

  // Thêm phương thức activate() để ẩn bàn phím khi màn hình được kích hoạt lại
  @override
  void activate() {
    super.activate();
    print("HomeScreen được kích hoạt lại, ẩn bàn phím");

    // Note: Overlay logic moved to pageHome.dart and pageHomeApprover.dart

    // Đảm bảo bàn phím bị ẩn khi quay lại màn hình này
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // Tắt toàn bộ focus và ẩn bàn phím
        FocusManager.instance.primaryFocus?.unfocus();
        _searchFocusNode.unfocus();
      }
    });
  }

  // Listener cho scroll
  void _scrollListener() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      if (!isLoadingMore && hasMoreItems) {
        _loadMoreActivities();
      }
    }
  }

  Future<void> _loadActivities() async {
    setState(() => _isLoading = true);
    try {
      final activities = await ApiService.getForm();
      setState(() {
        items = activities.take(itemsPerPage).toList();
        filteredItems = List.from(items);
        currentPage = 1; // Reset trang về 1
        hasMoreItems = activities.length > itemsPerPage; // Bật lại phân trang
        _isLoading = false;
      });
      print(
          'Đã tải ${items.length} activities, có thể tải thêm: $hasMoreItems');
    } catch (e) {
      print('Error loading activities: $e');
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadMoreActivities() async {
    if (isLoadingMore) return;

    setState(() => isLoadingMore = true);
    try {
      final activities = await ApiService.getForm();
      final startIndex = currentPage * itemsPerPage;
      final endIndex = startIndex + itemsPerPage;

      if (startIndex < activities.length) {
        setState(() {
          // Add new items while preserving existing ones
          items.addAll(activities.skip(startIndex).take(itemsPerPage));

          // Apply current filters to new items
          filteredItems = items.where((item) {
            bool matchSearch = _searchQuery.isEmpty ||
                item.title.toLowerCase().contains(_searchQuery.toLowerCase());
            bool matchStatus = statusFilter == null ||
                item.status.toLowerCase() == statusFilter!.toLowerCase();
            bool matchCategory =
                categoryFilter == null || item.categoryId == categoryFilter;

            DateTime itemDate = DateTime.parse(item.createdAt);
            bool matchDate = dateFilter == null ||
                (itemDate.year == dateFilter!.year &&
                    itemDate.month == dateFilter!.month &&
                    itemDate.day == dateFilter!.day);

            return matchSearch && matchStatus && matchCategory && matchDate;
          }).toList();

          currentPage++;
          hasMoreItems = endIndex < activities.length;
        });
      } else {
        setState(() => hasMoreItems = false);
      }
    } catch (e) {
      print('Error loading more activities: $e');
    } finally {
      setState(() => isLoadingMore = false);
    }
  }

  // Hàm để bỏ focus khỏi search field
  void _unfocusSearch() {
    _searchFocusNode.unfocus();
    // Thêm lệnh unfocus cho tất cả các focus node trong màn hình
    FocusScope.of(context).unfocus();
  }

  // Note: Overlay methods removed to prevent duplicate overlays
  // Overlay logic is now handled in pageHome.dart and pageHomeApprover.dart

  Future<void> filterItems() async {
    // Bỏ focus khỏi thanh tìm kiếm khi bắt đầu filter
    _unfocusSearch();

    setState(() => _isLoading = true);
    try {
      // Format date to YYYY-MM-DD if dateFilter exists
      String? formattedDate;
      if (dateFilter != null) {
        formattedDate =
            "${dateFilter!.year}-${dateFilter!.month.toString().padLeft(2, '0')}-${dateFilter!.day.toString().padLeft(2, '0')}";
      }

      // Tạo dữ liệu body cho request
      final Map<String, dynamic> requestData = {};

      // Chỉ thêm các tham số mà người dùng đã chọn filter
      if (statusFilter != null) {
        requestData['status'] = statusFilter;
      }

      if (categoryFilter != null) {
        // Thử dùng 'category' thay vì 'category_id'
        requestData['category'] = categoryFilter;

        // Hoặc có thể server cần category_name
        if (categoryFilter == 1) {
          requestData['category_name'] = 'Payment';
        } else if (categoryFilter == 2) {
          requestData['category_name'] = 'Invoice';
        } else {
          requestData['category_name'] = 'Tax';
        }
      }

      if (formattedDate != null) {
        requestData['created_at'] = formattedDate;
      }

      // Thêm tham số tìm kiếm nếu có
      if (_searchQuery.isNotEmpty) {
        requestData['search'] = _searchQuery;
      }

      print('Filter request data: $requestData');

      // Tạo instance của Dio
      final dio = Dio();

      // Thêm headers authorization
      dio.options.headers = ApiConfig.headers;

      // Gọi API post
      final response = await dio.post(
        '${ApiConfig.baseUrl}${ApiEndpoints.filterForm}',
        data: requestData,
      );

      print('Filter response status: ${response.statusCode}');
      print('Filter response data: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data;

        if (responseData['success'] == true && responseData['data'] != null) {
          final List<dynamic> data = responseData['data'];
          // Đảm bảo chuyển đổi mỗi item JSON thành đối tượng ActivityItem
          final List<ActivityItem> filteredActivities =
              data.map((json) => ActivityItem.fromJson(json)).toList();

          print('Đã lọc được ${filteredActivities.length} item');
          // In ra category của mỗi item để debug
          for (var item in filteredActivities) {
            print(
                'Item ID: ${item.id}, Category: ${item.categoryId}, Title: ${item.title}');
          }

          setState(() {
            filteredItems = filteredActivities;
            currentPage = 1; // Reset trang về 1
            hasMoreItems = false; // Tắt phân trang khi lọc
            _isLoading = false;
          });
        } else {
          // Trả về danh sách rỗng nếu không có kết quả
          setState(() {
            filteredItems = [];
            hasMoreItems = false;
            _isLoading = false;
          });
        }
      } else {
        throw Exception('Failed to filter activities: ${response.statusCode}');
      }
    } catch (e) {
      print('Error filtering activities: $e');
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi khi lọc dữ liệu: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _clearFilters() async {
    setState(() {
      statusFilter = null;
      categoryFilter = null;
      dateFilter = null;
      _searchQuery = '';
      _searchController.clear();
    });

    // Bỏ focus khỏi thanh tìm kiếm
    _unfocusSearch();

    // Tải lại danh sách ban đầu với phân trang
    await _loadActivities();
  }

  void showFilterDialog() {
    final l10n = AppLocalizations.of(context)!;

    // Đảm bảo đã bỏ focus khỏi thanh tìm kiếm
    _unfocusSearch();

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            // Define status options based on user role
            List<DropdownMenuItem<String>> statusItems;

            if (isApprover) {
              // For Approver role: only show 4 specific status options
              statusItems = [
                DropdownMenuItem(
                  value: 'fail',
                  child: const Text('Failed'),
                ),
                DropdownMenuItem(
                  value: 'pendingaccountant',
                  child: const Text('Pending Accountant'),
                ),
                DropdownMenuItem(
                  value: 'pendingapprover',
                  child: const Text('Pending Approver'),
                ),
                DropdownMenuItem(
                  value: 'approved',
                  child: const Text('Approved'),
                ),
              ];
            } else {
              // For non-Approver users: show all status options (existing behavior)
              statusItems = [
                DropdownMenuItem(
                  value: 'draft',
                  child: const Text('Draft'),
                ),
                DropdownMenuItem(
                  value: 'cancel',
                  child: const Text('Cancel'),
                ),
                DropdownMenuItem(
                  value: 'fail',
                  child: const Text('Failed'),
                ),
                DropdownMenuItem(
                  value: 'pendingaccountant',
                  child: const Text('Pending Accountant'),
                ),
                DropdownMenuItem(
                  value: 'pendingapprover',
                  child: const Text('Pending Approver'),
                ),
                DropdownMenuItem(
                  value: 'approved',
                  child: const Text('Approved'),
                ),
              ];
            }

            return AlertDialog(
              title: Text(l10n.filterTitle),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  DropdownButton<String>(
                    isExpanded: true,
                    hint: Text(l10n.status),
                    value: statusFilter,
                    items: statusItems,
                    onChanged: (value) {
                      setState(() => statusFilter = value);
                    },
                  ),
                  const SizedBox(height: 8),
                  DropdownButton<int>(
                    isExpanded: true,
                    hint: Text(l10n.category),
                    value: categoryFilter,
                    items: _buildCategoryItems(),
                    onChanged: (value) {
                      setState(() => categoryFilter = value);
                    },
                  ),
                  const SizedBox(height: 8),
                  InkWell(
                    onTap: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: dateFilter ?? DateTime.now(),
                        firstDate: DateTime(2020),
                        lastDate: DateTime.now(),
                      );
                      if (date != null) {
                        setState(() => dateFilter = date);
                      }
                    },
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            dateFilter == null
                                ? l10n.selectDate
                                : '${l10n.date}: ${dateFilter!.day}/${dateFilter!.month}/${dateFilter!.year}',
                            style: TextStyle(
                              color: dateFilter == null
                                  ? Colors.black
                                  : Colors.black,
                              fontSize: 16,
                            ),
                          ),
                          const Icon(Icons.calendar_today,
                              color: Colors.black54),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    // Xóa bộ lọc trong dialog
                    setState(() {
                      statusFilter = null;
                      categoryFilter = null;
                      dateFilter = null;
                    });
                  },
                  child: Text(l10n.clearFilter),
                ),
                TextButton(
                  onPressed: () {
                    // Validate category filter based on user role before applying
                    if (!isApprover && categoryFilter == 3) {
                      // Clear Tax category filter for Submitter users
                      categoryFilter = null;
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                              'Tax category is not available for your role'),
                          backgroundColor: Colors.orange,
                        ),
                      );
                    }

                    Navigator.pop(context);
                    // Nếu đã xóa hết bộ lọc, tải lại ban đầu
                    if (statusFilter == null &&
                        categoryFilter == null &&
                        dateFilter == null) {
                      _clearFilters();
                    } else {
                      // Nếu còn filter, gọi API filter
                      filterItems();
                    }
                  },
                  child: Text(l10n.applyFilter),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void showDetailDialog(ActivityItem item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Chi tiết #${item.id}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Người mua hàng:', item.buyerName),
              const SizedBox(height: 8),
              _buildDetailRow('Mã số thuế:', item.taxCode),
              const SizedBox(height: 8),
              _buildDetailRow('Loại tiền:', item.currency),
              const SizedBox(height: 8),
              _buildDetailRow('Số tiền:', '${item.price} ${item.currency}'),
              const SizedBox(height: 8),
              if (item.currency != 'VND' && item.swiftCode != null) ...[
                _buildDetailRow('Swift Code:', item.swiftCode!),
                const SizedBox(height: 8),
              ],
              _buildDetailRow('Mô tả:', item.description),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Đóng'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Expanded(
          child: Text(value),
        ),
      ],
    );
  }

  Future<void> _refreshData() async {
    // If user is currently searching, don't refresh to preserve search results
    if (isInSearchMode) {
      print('HomeScreen - Skipping refresh because user is in search mode');
      return;
    }

    try {
      final activities = await ApiService.getForm();
      setState(() {
        currentPage = 1; // Reset pagination
        items = activities.take(itemsPerPage).toList();
        // Apply current filters
        filteredItems = items.where((item) {
          bool matchSearch = _searchQuery.isEmpty ||
              item.title.toLowerCase().contains(_searchQuery.toLowerCase());
          bool matchStatus = statusFilter == null ||
              item.status.toLowerCase() == statusFilter!.toLowerCase();
          bool matchCategory =
              categoryFilter == null || item.categoryId == categoryFilter;

          DateTime itemDate = DateTime.parse(item.createdAt);
          bool matchDate = dateFilter == null ||
              (itemDate.year == dateFilter!.year &&
                  itemDate.month == dateFilter!.month &&
                  itemDate.day == dateFilter!.day);

          return matchSearch && matchStatus && matchCategory && matchDate;
        }).toList();
        hasMoreItems = activities.length > itemsPerPage;
      });
    } catch (e) {
      print('Error refreshing data: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Error refreshing data')),
        );
      }
    }
  }

  // Add method to check role
  Future<void> _checkRole() async {
    final prefs = await SharedPreferences.getInstance();
    final userRole = prefs.getString('user_role');
    setState(() {
      isApprover = userRole == 'Approver';

      // If user is Submitter and has Tax category filter selected, clear it
      if (!isApprover && categoryFilter == 3) {
        categoryFilter = null;
        print('HomeScreen - Cleared Tax category filter for Submitter user');
      }
    });
  }

  // Build category dropdown items based on user role
  List<DropdownMenuItem<int>> _buildCategoryItems() {
    List<DropdownMenuItem<int>> categoryItems = [
      const DropdownMenuItem(
        value: 1,
        child: Text('Payment'),
      ),
      const DropdownMenuItem(
        value: 2,
        child: Text('Invoice'),
      ),
    ];

    // Only show Tax category for Approver users
    if (isApprover) {
      categoryItems.add(
        const DropdownMenuItem(
          value: 3,
          child: Text('Tax'),
        ),
      );
    }

    return categoryItems;
  }

  // Public method to check if user is currently searching
  bool get isInSearchMode {
    return _searchController.text.isNotEmpty;
  }

  // Public method to force refresh from external sources
  Future<void> forceRefresh() async {
    print('HomeScreen - forceRefresh called from external source');
    await _refreshData();
  }

  // Public method to force refresh even when in search mode (for special cases)
  Future<void> forceRefreshIgnoreSearch() async {
    print('HomeScreen - forceRefreshIgnoreSearch called from external source');
    final wasInSearchMode = isInSearchMode;

    try {
      final activities = await ApiService.getForm();
      setState(() {
        currentPage = 1; // Reset pagination
        items = activities.take(itemsPerPage).toList();

        // If was in search mode, re-apply search
        if (wasInSearchMode && _searchController.text.isNotEmpty) {
          // Re-trigger search to get fresh results
          searchFormItems(_searchController.text);
        } else {
          // Apply current filters
          filteredItems = items.where((item) {
            bool matchSearch = _searchQuery.isEmpty ||
                item.title.toLowerCase().contains(_searchQuery.toLowerCase());
            bool matchStatus = statusFilter == null ||
                item.status.toLowerCase() == statusFilter!.toLowerCase();
            bool matchCategory =
                categoryFilter == null || item.categoryId == categoryFilter;

            DateTime itemDate = DateTime.parse(item.createdAt);
            bool matchDate = dateFilter == null ||
                (itemDate.year == dateFilter!.year &&
                    itemDate.month == dateFilter!.month &&
                    itemDate.day == dateFilter!.day);

            return matchSearch && matchStatus && matchCategory && matchDate;
          }).toList();
        }

        hasMoreItems = activities.length > itemsPerPage;
      });
    } catch (e) {
      print('Error force refreshing data: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Error refreshing data')),
        );
      }
    }
  }

  // Hàm tìm kiếm mới sử dụng searchForm API
  Future<void> searchFormItems(String query) async {
    // Bỏ focus khỏi thanh tìm kiếm khi bắt đầu tìm kiếm
    _unfocusSearch();

    if (query.isEmpty) {
      await _clearFilters();
      return;
    }

    // Chỉ cập nhật trạng thái tìm kiếm thay vì reload toàn bộ trang
    setState(() => isSearching = true);

    try {
      // Tạo instance của Dio
      final dio = Dio();

      // Lấy token từ SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? '';

      // Thêm headers authorization
      dio.options.headers = {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };

      // Gọi API searchForm
      final response = await dio.post(
        '${ApiConfig.baseUrl}${ApiEndpoints.searchForm}',
        queryParameters: {
          'title': query,
        },
      );

      print('Search response status: ${response.statusCode}');
      print('Search response data: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data;

        if (responseData['success'] == true && responseData['data'] != null) {
          final List<dynamic> data = responseData['data'];
          // Chuyển đổi mỗi item JSON thành đối tượng ActivityItem
          final List<ActivityItem> searchResults =
              data.map((json) => ActivityItem.fromJson(json)).toList();

          print('Đã tìm được ${searchResults.length} item');

          setState(() {
            filteredItems = searchResults;
            hasMoreItems = false; // Tắt phân trang khi tìm kiếm
            isSearching = false; // Kết thúc tìm kiếm
          });
        } else {
          // Trả về danh sách rỗng nếu không có kết quả
          setState(() {
            filteredItems = [];
            hasMoreItems = false;
            isSearching = false; // Kết thúc tìm kiếm
          });
        }
      } else {
        throw Exception('Failed to search activities: ${response.statusCode}');
      }
    } catch (e) {
      print('Error searching activities: $e');
      setState(() {
        isSearching = false; // Kết thúc tìm kiếm khi có lỗi
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi khi tìm kiếm: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    if (_isLoading) {
      return const Scaffold(
        body: LoadingOverlay(),
      );
    }

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        scrolledUnderElevation: 0,
        surfaceTintColor: Colors.white,
        backgroundColor: Colors.white,
        elevation: 0,
        title: Row(
          children: [
            Text(
              '${l10n.list} (${filteredItems.length})',
              style: const TextStyle(
                color: Colors.black,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 8),
          ],
        ),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1.0),
          child: Container(
            color: Colors.grey.shade600,
            height: 1.0,
          ),
        ),
      ),
      body: GestureDetector(
        onTap: () {
          _unfocusSearch();
        },
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _searchController,
                      focusNode: _searchFocusNode,
                      textInputAction: TextInputAction.search,
                      decoration: InputDecoration(
                        hintText: l10n.searchHint,
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding:
                            const EdgeInsets.symmetric(horizontal: 16),
                      ),
                      onSubmitted: (value) {
                        _unfocusSearch();
                        if (value.isNotEmpty) {
                          searchFormItems(value);
                        }
                      },
                      onChanged: (value) {
                        if (_debounce?.isActive ?? false) _debounce!.cancel();
                        _debounce =
                            Timer(const Duration(milliseconds: 500), () {
                          setState(() {
                            _searchQuery = value;

                            if (value.isEmpty) {
                              _clearFilters();
                            } else {
                              searchFormItems(value);
                            }
                          });
                        });
                      },
                      onEditingComplete: () {
                        _unfocusSearch();
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.filter_list),
                      color: Colors.white,
                      onPressed: () {
                        // Bỏ focus trước khi mở dialog filter
                        _unfocusSearch();
                        showFilterDialog();
                      },
                      tooltip: l10n.filterTooltip,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: RefreshIndicator(
                onRefresh: _refreshData,
                color: AppColors.primary,
                strokeWidth: 2.0,
                displacement: 40.0,
                child: Stack(
                  children: [
                    ListView.builder(
                      physics: const AlwaysScrollableScrollPhysics(),
                      controller: _scrollController,
                      padding: const EdgeInsets.all(8),
                      itemCount: filteredItems.length,
                      itemBuilder: (context, index) {
                        return ActivityScreen(
                          item: filteredItems[index],
                          onRefresh: _refreshData,
                          // No onStatusChanged needed for homeScreen - users stay on home
                        );
                      },
                    ),
                    // Hiển thị loading indicator khi đang tìm kiếm
                    if (isSearching)
                      const Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            CircularProgressIndicator(),
                            SizedBox(height: 16),
                            Text('Đang tìm kiếm...'),
                          ],
                        ),
                      ),
                    // Hiển thị thông báo khi không có kết quả
                    if (!isSearching && filteredItems.isEmpty)
                      Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.search_off,
                                size: 48, color: Colors.grey),
                            const SizedBox(height: 16),
                            Text(
                              'Không tìm thấy kết quả',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey[700],
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
