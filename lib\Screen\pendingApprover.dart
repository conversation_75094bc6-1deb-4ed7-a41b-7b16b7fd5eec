import 'package:du_an_flutter/Screen/listActivities.dart';
import 'package:du_an_flutter/model/activity_item.dart';
import 'package:du_an_flutter/services/api_service.dart';
import 'package:du_an_flutter/widgets/loading_overlay.dart';
import 'package:du_an_flutter/l10n/app_localizations.dart';
import 'package:flutter/material.dart';

// Export the state class
typedef PendingApproverScreenState = _PendingApproverScreenState;

class PendingApproverScreen extends StatefulWidget {
  const PendingApproverScreen({super.key});

  @override
  State<PendingApproverScreen> createState() => _PendingApproverScreenState();
}

class _PendingApproverScreenState extends State<PendingApproverScreen> {
  List<ActivityItem> items = [];

  // Add loading state
  bool get isLoading => _isLoading;
  bool _isLoading = true;
  DateTime? _lastLoadTime;

  // Static instance for external refresh triggers
  static _PendingApproverScreenState? _instance;

  // Static method to trigger refresh from external sources (like ApprovalService)
  static Future<void> triggerRefresh() async {
    print('PendingApproverScreen - triggerRefresh called from external source');
    if (_instance != null && _instance!.mounted) {
      print('PendingApproverScreen - Instance available, triggering refresh');
      _instance!.forceReload();
    } else {
      print('PendingApproverScreen - Instance not available or not mounted');
    }
  }

  final List<Map<String, dynamic>> statusItems = [
    {
      'title': 'Fail',
      'count': 0,
      'color': Colors.red,
    },
    {
      'title': 'PendingAccountant',
      'count': 0,
      'color': Colors.amber,
    },
    {
      'title': 'PendingApprover',
      'count': 0,
      'color': Colors.lightBlue,
    },
    {
      'title': 'Approved',
      'count': 0,
      'color': Colors.lightGreen,
    },
  ];

  @override
  void initState() {
    super.initState();
    print("PendingApprovalsScreen - initState called");
    _instance = this; // Set static instance
    _loadActivities();
    _updateStatusCounts();
  }

  @override
  void dispose() {
    super.dispose();
    print("PendingApprovalsScreen - dispose called");
    if (_instance == this) {
      _instance = null; // Clear static instance
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("PendingApprovalsScreen - didChangeDependencies called");
    _loadActivities();
  }

  @override
  void activate() {
    super.activate();
    print("PendingApprovalsScreen - activate called - RELOADING API DATA");
    // Force reload when tab is activated
    _loadActivities();
  }

  // Force reload method for manual refresh
  void forceReload() {
    print("PendingApprovalsScreen - forceReload called");
    _lastLoadTime = null;
    _loadActivities();
  }

  // Update status counts
  void _updateStatusCounts() {
    if (items.isNotEmpty) {
      // Count activities by status
      for (var item in statusItems) {
        final String statusTitle = item['title'];
        final count = items
            .where((activity) =>
                activity.status.toLowerCase() == statusTitle.toLowerCase())
            .length;
        item['count'] = count;
      }
    }
  }

  Future<void> _loadActivities() async {
    print("PendingApprovalsScreen - _loadActivities started");
    if (!mounted) {
      print("PendingApprovalsScreen - not mounted, aborting _loadActivities");
      return;
    }

    setState(() => _isLoading = true);
    print("PendingApprovalsScreen - set loading state to true");

    try {
      print("PendingApprovalsScreen - calling API.getForm()");
      final activities = await ApiService.getForm();
      print(
          "PendingApprovalsScreen - API.getForm() completed with ${activities.length} items");

      if (!mounted) {
        print("PendingApprovalsScreen - not mounted after API call, aborting");
        return;
      }

      setState(() {
        items = activities;
        _isLoading = false;
        _lastLoadTime = DateTime.now();
        print(
            "PendingApprovalsScreen - updated state with new data, timestamp: ${_lastLoadTime}");
      });

      // Update counts after getting new data
      _updateStatusCounts();
    } catch (e) {
      print('PendingApprovalsScreen - Error loading activities: $e');
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: LoadingOverlay(),
      );
    }

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          AppLocalizations.of(context).approvalStatus,
          style: const TextStyle(color: Colors.black),
        ),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1.0),
          child: Container(
            color: Colors.grey.shade600,
            height: 1.0,
          ),
        ),
      ),
      body: RefreshIndicator(
        onRefresh: _loadActivities,
        child: ListView.separated(
          itemCount: statusItems.length,
          separatorBuilder: (_, __) => const Divider(height: 0),
          itemBuilder: (context, index) {
            return ListTile(
              title: Text(
                statusItems[index]['title']!,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: statusItems[index]['color'],
                      borderRadius: BorderRadius.circular(5),
                    ),
                    child: Text(
                      '${statusItems[index]['count']}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 10),
                  const Icon(Icons.chevron_right),
                ],
              ),
              onTap: () async {
                final filteredActivities = items
                    .where((item) =>
                        item.status.toLowerCase() ==
                        statusItems[index]['title'].toLowerCase())
                    .toList();

                // Update immediately when returning from the next screen
                await Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => Listactivities(
                      status: statusItems[index]['title'],
                      activities: filteredActivities,
                    ),
                  ),
                );

                // Reload data when coming back
                _loadActivities();
              },
            );
          },
        ),
      ),
    );
  }
}
