import 'dart:convert';
import 'dart:io';
import 'package:du_an_flutter/APIconfig/api_endpoints.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:du_an_flutter/APIconfig/api_config.dart';
import 'package:du_an_flutter/L10n/l10n.dart';
import 'package:du_an_flutter/constants/colors.dart';
import 'package:du_an_flutter/l10n/app_localizations.dart';
import 'package:du_an_flutter/page/pageLogin.dart';
import 'package:du_an_flutter/providers/locale_provider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart' as ptr;
import 'package:dio/dio.dart';
import 'package:flutter/services.dart';
import 'package:du_an_flutter/widgets/loading_overlay.dart';

// Export ProfilePageState
typedef ProfilePageState = _ProfilePageState;

class ProfilePage extends StatefulWidget {
  // Thêm constructor
  const ProfilePage({super.key});

  // Static reference to the current state to allow tab navigation checking
  static _ProfilePageState? of(BuildContext context) {
    print(
        "ProfilePage.of được gọi: ${context.findAncestorStateOfType<_ProfilePageState>() != null}");
    return context.findAncestorStateOfType<_ProfilePageState>();
  }

  @override
  _ProfilePageState createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> with WidgetsBindingObserver {
  late TextEditingController _nameController;
  late TextEditingController _emailController;
  late TextEditingController _phoneController;
  late TextEditingController _addressController;
  // Add new controllers
  late TextEditingController _cccdController;
  late TextEditingController _grantedDateController;
  late TextEditingController _birthdayController;
  // Add bank controllers
  late TextEditingController _bankNameController;
  late TextEditingController _bankNumberController;
  late TextEditingController _accountHolderController;
  bool _isEdited = false;
  bool _isLoading = true;
  bool _isSubmitting = false;
  Map<String, dynamic>? userData;
  XFile? _selectedImage;
  final ImagePicker _picker = ImagePicker();
  List<Map<String, String>> banks = [];

  // Biến để theo dõi xem profile tab có đang được hiển thị hay không
  bool _isActive = false;

  // Make isEdited accessible from outside
  bool get isEdited => _isEdited;

  // Add RefreshController
  final ptr.RefreshController _refreshController = ptr.RefreshController(
    initialRefresh: false,
  );

  @override
  void initState() {
    super.initState();
    // Đăng ký observer để theo dõi trạng thái của app
    WidgetsBinding.instance.addObserver(this);

    // Initialize new controllers
    _cccdController = TextEditingController();
    _grantedDateController = TextEditingController();
    _birthdayController = TextEditingController();

    _nameController = TextEditingController();
    _emailController = TextEditingController();
    _phoneController = TextEditingController();
    // Make phone controller only accept digits
    _phoneController.addListener(() {
      final text = _phoneController.text;
      if (text.isNotEmpty && !RegExp(r'^[0-9]+$').hasMatch(text)) {
        // If text contains non-digits, remove them
        final digitsOnly = text.replaceAll(RegExp(r'[^0-9]'), '');
        _phoneController.value = TextEditingValue(
          text: digitsOnly,
          selection: TextSelection.collapsed(offset: digitsOnly.length),
        );
      }
    });

    // Make CCCD controller only accept digits
    _cccdController.addListener(() {
      final text = _cccdController.text;
      if (text.isNotEmpty && !RegExp(r'^[0-9]+$').hasMatch(text)) {
        // If text contains non-digits, remove them
        final digitsOnly = text.replaceAll(RegExp(r'[^0-9]'), '');
        _cccdController.value = TextEditingValue(
          text: digitsOnly,
          selection: TextSelection.collapsed(offset: digitsOnly.length),
        );
      }
    });

    _addressController = TextEditingController();

    // Initialize bank controllers
    _bankNameController = TextEditingController();
    _bankNumberController = TextEditingController();
    _accountHolderController = TextEditingController();

    // Add listeners for all controllers to detect changes
    _cccdController.addListener(_checkIfEdited);
    _grantedDateController.addListener(_checkIfEdited);
    _birthdayController.addListener(_checkIfEdited);

    _nameController.addListener(_checkIfEdited);
    _emailController.addListener(_checkIfEdited);
    _phoneController.addListener(_checkIfEdited);
    _addressController.addListener(_checkIfEdited);

    // Add listeners
    _bankNameController.addListener(_checkIfEdited);
    _bankNumberController.addListener(_checkIfEdited);
    _accountHolderController.addListener(_checkIfEdited);

    // Tải dữ liệu người dùng khi khởi tạo
    _loadUserData();

    // Đăng ký callback cho lần render frame đầu tiên để đánh dấu màn hình đang active
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _isActive = true;
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Thực hiện các tác vụ cần thiết khi dependencies thay đổi
  }

  @override
  void didUpdateWidget(ProfilePage oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Có thể được gọi khi widget được cập nhật
  }

  @override
  void activate() {
    super.activate();
    print("ProfileScreen được kích hoạt");
    // Đánh dấu màn hình đang được active
    _isActive = true;

    // Đảm bảo bàn phím bị ẩn khi quay lại màn hình này
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // Tắt toàn bộ focus và ẩn bàn phím
        FocusManager.instance.primaryFocus?.unfocus();
      }
    });

    // Gọi API để tải dữ liệu mới khi màn hình được kích hoạt
    _loadUserData();
  }

  @override
  void deactivate() {
    // Đánh dấu màn hình không còn active
    _isActive = false;
    super.deactivate();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // Nếu app trở lại foreground và màn hình profile đang active
    if (state == AppLifecycleState.resumed && _isActive) {
      print("App resumed và ProfileScreen đang active, tải lại dữ liệu");
      _loadUserData();
    }
  }

  Future<void> _loadUserData() async {
    try {
      final dio = Dio();
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? '';

      if (token.isEmpty) {
        throw Exception('Token không hợp lệ');
      }

      final response = await dio.get(
        '${ApiConfig.baseUrl}${ApiEndpoints.getProfile}',
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );

      print('=== GET PROFILE DEBUG ===');
      print('Response Status: ${response.statusCode}');
      print('Response Data: ${response.data}');

      if (response.statusCode == 200) {
        // Đảm bảo không có focus trước khi set state
        FocusManager.instance.primaryFocus?.unfocus();

        setState(() {
          userData = response.data['data'];

          // Add debug logs to check organization data
          print('=== ORGANIZATION DEBUG INFO ===');
          print('userData: $userData');
          print(
              'currentOrganization data: ${userData?['currentOrganization']}');
          print('organization data: ${userData?['organization']}');
          print('user_organizations data: ${userData?['user_organizations']}');

          // Check banks data
          if (userData?['banks'] != null) {
            print('Banks data: ${userData?['banks']}');
            try {
              banks = (userData?['banks'] as List?)
                      ?.map((bank) {
                        if (bank == null) return null;
                        // Ensure we get the ID field from API response
                        final id = bank['id']?.toString() ?? '';
                        // Handle both old and new bank formats with null safety
                        final bankName = bank['bankName']?.toString() ??
                            bank['bank_name']?.toString() ??
                            '';
                        final bankNumber = bank['bankNumber']?.toString() ??
                            bank['card_number']?.toString() ??
                            '';
                        final accountHolder =
                            bank['accountHolder']?.toString() ??
                                bank['account_name']?.toString() ??
                                '';

                        return {
                          'id': id,
                          'bankName': bankName,
                          'bankNumber': bankNumber,
                          'accountHolder': accountHolder,
                        };
                      })
                      .whereType<Map<String, String>>()
                      .toList() ??
                  [];

              print('Parsed banks: $banks');

              if (banks.isNotEmpty) {
                _bankNameController.text = banks[0]['bankName'] ?? '';
                _bankNumberController.text = banks[0]['bankNumber'] ?? '';
                _accountHolderController.text = banks[0]['accountHolder'] ?? '';
              }
            } catch (e) {
              print('Error parsing banks data: $e');
              banks = [];
            }
          }

          // Set other user data
          _nameController.text = userData?['fullname'] ?? '';
          _emailController.text = userData?['email'] ?? '';
          _phoneController.text = userData?['phone'] ?? '';

          // Enhanced organization name handling with proper null safety
          String orgName = '';
          try {
            // First priority: Get from currentOrganization
            if (userData?['currentOrganization'] is Map) {
              orgName = userData?['currentOrganization']?['name'] ?? '';
              print('Found organization name in currentOrganization: $orgName');
            }

            // Second priority: Try to get org name from direct organization field
            if (orgName.isEmpty && userData?['organization'] is Map) {
              orgName = userData?['organization']?['name'] ?? '';
              print('Found organization name in organization field: $orgName');
            }

            // Third priority: Try user_organizations array
            if (orgName.isEmpty &&
                userData?['user_organizations'] is List &&
                (userData?['user_organizations'] as List).isNotEmpty) {
              var userOrg = userData?['user_organizations'][0];
              if (userOrg is Map && userOrg['organization'] is Map) {
                orgName = userOrg['organization']?['name'] ?? '';
                print(
                    'Found organization name in user_organizations array: $orgName');
              }
            }

            print('Final extracted organization name: "$orgName"');
          } catch (e) {
            print('Error extracting organization name: $e');
          }

          _addressController.text = orgName;

          _cccdController.text = userData?['citizenId'] ?? '';
          _grantedDateController.text =
              userData?['grantedDate']?.toString().split(' ')[0] ?? '';
          _birthdayController.text = userData?['birthday'] ?? '';

          // Update local storage with fresh data
          prefs.setString('user_data', jsonEncode(userData));

          _isLoading = false;
        });

        // Đảm bảo không có focus sau khi set state
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            FocusManager.instance.primaryFocus?.unfocus();
          }
        });
      } else {
        throw Exception('Failed to load profile data');
      }
    } catch (e) {
      print('Error loading user data: $e');
      print('Stack trace: ${StackTrace.current}');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi tải thông tin: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
      setState(() => _isLoading = false);
    }
  }

  // Add refresh method
  Future<void> _onRefresh() async {
    try {
      await _loadUserData();
      _refreshController.refreshCompleted();
    } catch (e) {
      _refreshController.refreshFailed();
    }
  }

  @override
  void dispose() {
    // Hủy đăng ký observer
    WidgetsBinding.instance.removeObserver(this);

    _refreshController.dispose();
    // Dispose new controllers
    _cccdController.dispose();
    _grantedDateController.dispose();
    _birthdayController.dispose();
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    // Dispose bank controllers
    _bankNameController.dispose();
    _bankNumberController.dispose();
    _accountHolderController.dispose();
    super.dispose();
  }

  void _checkIfEdited() {
    // Extract organization name with proper null safety
    String orgName = '';
    try {
      // First priority: Get from currentOrganization
      if (userData?['currentOrganization'] is Map) {
        orgName = userData?['currentOrganization']?['name'] ?? '';
      }

      // Second priority: Try to get org name from direct organization field
      if (orgName.isEmpty && userData?['organization'] is Map) {
        orgName = userData?['organization']?['name'] ?? '';
      }

      // Third priority: Try user_organizations array
      if (orgName.isEmpty &&
          userData?['user_organizations'] is List &&
          (userData?['user_organizations'] as List).isNotEmpty) {
        var userOrg = userData?['user_organizations'][0];
        if (userOrg is Map && userOrg['organization'] is Map) {
          orgName = userOrg['organization']?['name'] ?? '';
        }
      }
    } catch (e) {
      print('Error in _checkIfEdited: $e');
    }

    // Use null-safe comparisons with default empty strings
    final isEdited = _nameController.text != (userData?["fullname"] ?? '') ||
        _emailController.text != (userData?["email"] ?? '') ||
        _phoneController.text != (userData?["phone"] ?? '') ||
        _addressController.text != orgName ||
        _cccdController.text != (userData?["citizenId"] ?? '') ||
        _grantedDateController.text !=
            (userData?["grantedDate"]?.toString().split(' ')[0] ?? '') ||
        _birthdayController.text != (userData?["birthday"] ?? '');

    print("_checkIfEdited được gọi: $isEdited");
    if (_nameController.text != (userData?["fullname"] ?? '')) {
      print(
          "Name thay đổi: '${_nameController.text}' vs '${userData?["fullname"]}'");
    }

    if (isEdited != _isEdited) {
      setState(() {
        _isEdited = isEdited;
        print("_isEdited đã được cập nhật: $_isEdited");
      });
    }
  }

  void _saveChanges() async {
    // Ẩn bàn phím
    FocusScope.of(context).unfocus();

    final l10n = AppLocalizations.of(context)!;

    // Validate that no field is empty
    if (_nameController.text.isEmpty ||
        _emailController.text.isEmpty ||
        _phoneController.text.isEmpty ||
        _addressController.text.isEmpty ||
        _cccdController.text.isEmpty ||
        _grantedDateController.text.isEmpty ||
        _birthdayController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Vui lòng điền đầy đủ tất cả các trường thông tin'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Validate phone number is numeric
    if (_phoneController.text.isNotEmpty &&
        !RegExp(r'^[0-9]+$').hasMatch(_phoneController.text)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Số điện thoại chỉ được nhập số'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() => _isSubmitting = true);

    try {
      final dio = Dio();
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? '';

      if (token.isEmpty) {
        throw Exception('Token không hợp lệ');
      }

      final formData = {
        'fullname': _nameController.text,
        'email': _emailController.text,
        'phone': _phoneController.text,
        'organization_name': _addressController.text,
        'citizenId': _cccdController.text,
        'grantedDate': _grantedDateController.text,
        'birthday': _birthdayController.text,
      };

      print('=== UPDATE PROFILE DEBUG ===');
      print('Request Data: $formData');

      // Update profile
      final updateResponse = await dio.post(
        '${ApiConfig.baseUrl}${ApiEndpoints.upDateProfile}',
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
        data: formData,
      );

      print('Update Response Status: ${updateResponse.statusCode}');
      print('Update Response Data: ${updateResponse.data}');

      if (updateResponse.statusCode == 200) {
        // Get fresh data from API
        final getProfileResponse = await dio.get(
          '${ApiConfig.baseUrl}${ApiEndpoints.getProfile}',
          options: Options(
            headers: {
              'Authorization': 'Bearer $token',
              'Content-Type': 'application/json',
            },
          ),
        );

        if (getProfileResponse.statusCode == 200) {
          // Update SharedPreferences with new data
          await prefs.setString(
              'user_data', jsonEncode(getProfileResponse.data['data']));

          // Reload UI with new data
          await _loadUserData();

          // Reset edited flag after successful save
          _isEdited = false;

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(l10n.updateSuccess)),
            );
          }
        } else {
          throw Exception('Failed to get updated profile data');
        }
      } else {
        throw Exception('Failed to update profile');
      }
    } catch (e) {
      print('Error updating profile: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi cập nhật thông tin: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isSubmitting = false);
    }
  }

  Future<void> _pickImageFromGallery() async {
    // Ẩn bàn phím
    FocusScope.of(context).unfocus();

    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 70, // Thêm nén ảnh để giảm kích thước
      );
      if (image != null) {
        setState(() {
          _selectedImage = image;
          userData?["avatarUrl"] = image.path;
        });
        // Upload image ngay sau khi chọn
        await _uploadImageToServer(image.path);
      }
    } catch (e) {
      print('Error picking image from gallery: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Lỗi chọn ảnh: $e')),
        );
      }
    }
  }

  Future<void> _pickImageFromCamera() async {
    // Ẩn bàn phím
    FocusScope.of(context).unfocus();

    try {
      final XFile? photo = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 70, // Thêm nén ảnh để giảm kích thước
      );
      if (photo != null) {
        setState(() {
          _selectedImage = photo;
          userData?["avatarUrl"] = photo.path;
        });
        // Upload image ngay sau khi chụp
        await _uploadImageToServer(photo.path);
      }
    } catch (e) {
      print('Error taking photo: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Lỗi chụp ảnh: $e')),
        );
      }
    }
  }

  void _showImagePickerOptions() {
    // Ẩn bàn phím
    FocusScope.of(context).unfocus();

    final l10n = AppLocalizations.of(context);

    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(Icons.photo_library, color: AppColors.primary),
                ),
                title: Text(l10n.selectFromGallery),
                onTap: () {
                  Navigator.pop(context);
                  _pickImageFromGallery();
                },
              ),
              ListTile(
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(Icons.camera_alt, color: AppColors.primary),
                ),
                title: Text(l10n.takeNewPhoto),
                onTap: () {
                  Navigator.pop(context);
                  _pickImageFromCamera();
                },
              ),
              const SizedBox(height: 8),
            ],
          ),
        );
      },
    );
  }

  // Add date picker methods
  Future<void> _selectDate(
      BuildContext context, TextEditingController controller) async {
    // Tạo FocusScopeNode để kiểm soát focus
    final FocusScopeNode currentFocus = FocusScope.of(context);

    // Ẩn bàn phím
    if (!currentFocus.hasPrimaryFocus) {
      currentFocus.unfocus();
    }

    // Delay nhỏ trước khi hiển thị date picker để đảm bảo bàn phím đã đóng hoàn toàn
    await Future.delayed(const Duration(milliseconds: 100));

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      setState(() {
        controller.text =
            "${picked.year}-${picked.month.toString().padLeft(2, '0')}-${picked.day.toString().padLeft(2, '0')}";
      });
    }

    // Đảm bảo không có field nào được focus sau khi date picker đóng
    if (currentFocus.hasPrimaryFocus) {
      currentFocus.unfocus();
    }
  }

  void _showBankSelectionDialog() {
    // Ẩn bàn phím
    FocusScope.of(context).unfocus();

    final l10n = AppLocalizations.of(context);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(l10n.selectBank),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: banks
                  .map((bank) => ListTile(
                        title: Text(bank['bankName'] ?? ''),
                        subtitle: Text(bank['bankNumber'] ?? ''),
                        onTap: () {
                          setState(() {
                            _bankNameController.text = bank['bankName'] ?? '';
                            _bankNumberController.text =
                                bank['bankNumber'] ?? '';
                            _accountHolderController.text =
                                bank['accountHolder'] ?? '';
                          });
                          Navigator.pop(context);
                        },
                      ))
                  .toList(),
            ),
          ),
        );
      },
    );
  }

  Future<void> _uploadImageToServer(String imagePath) async {
    try {
      print('Starting image upload from path: $imagePath');

      final file = File(imagePath);
      if (!await file.exists()) {
        throw Exception('File không tồn tại trước khi upload');
      }

      final dio = Dio();
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? '';

      if (token.isEmpty) {
        throw Exception('Token không hợp lệ');
      }

      // Create FormData with the image
      final formData = FormData.fromMap({
        'image': await MultipartFile.fromFile(
          imagePath,
          filename: imagePath.split('/').last,
        ),
      });

      print('Sending image upload request...');
      final response = await dio.post(
        '${ApiConfig.baseUrl}${ApiEndpoints.upDateProfileImage}',
        data: formData,
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'multipart/form-data',
          },
        ),
        onSendProgress: (sent, total) {
          if (total != -1) {
            print(
                'Upload Progress: ${(sent / total * 100).toStringAsFixed(0)}%');
          }
        },
      );

      print('Upload Response Status: ${response.statusCode}');
      print('Upload Response Data: ${response.data}');

      if (response.statusCode == 200) {
        await _loadUserData();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Cập nhật ảnh đại diện thành công'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        throw Exception('Upload failed with status: ${response.statusCode}');
      }
    } catch (e) {
      print('Error uploading image: $e');
      print('Stack trace: ${StackTrace.current}');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi cập nhật ảnh: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Handle tab navigation with confirmation dialog
  Future<bool> handleTabNavigation() async {
    print("handleTabNavigation được gọi: _isEdited = $_isEdited");

    // Ẩn bàn phím ngay lập tức
    FocusManager.instance.primaryFocus?.unfocus();

    // Đảm bảo đợi bàn phím đóng hoàn toàn
    await Future.delayed(const Duration(milliseconds: 50));

    if (!_isEdited) {
      print("Không có thay đổi, cho phép chuyển tab");
      return true; // No changes, can navigate away
    }

    // Show confirmation dialog
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Dữ liệu chưa được lưu'),
        content: const Text(
            'Bạn đang có thông tin chưa lưu. Bạn có muốn rời đi không?'),
        actions: [
          TextButton(
            style: TextButton.styleFrom(
              foregroundColor: AppColors.primary,
            ),
            onPressed: () {
              print("Đã chọn 'Ở lại'");
              // Ẩn bàn phím khi đóng dialog
              FocusManager.instance.primaryFocus?.unfocus();
              Navigator.of(context).pop(false); // Stay on page
            },
            child: const Text('Ở lại'),
          ),
          TextButton(
            style: TextButton.styleFrom(
              foregroundColor: Colors.grey,
            ),
            onPressed: () {
              print("Đã chọn 'Không lưu'");
              // Ẩn bàn phím khi đóng dialog
              FocusManager.instance.primaryFocus?.unfocus();
              Navigator.of(context)
                  .pop(true); // Don't save, continue navigation
            },
            child: const Text('Không lưu'),
          ),
        ],
      ),
    );

    print("Kết quả dialog: $result");
    if (result == true) {
      // Don't save but navigate away
      print("Không lưu và chuyển tab");
      setState(() {
        // Reset _isEdited when choosing to navigate without saving
        _isEdited = false;
      });

      // Tải lại dữ liệu từ API để hủy bỏ các thay đổi chưa lưu
      Future.delayed(Duration.zero, () {
        if (mounted) {
          _loadUserData();
        }
      });

      // Đảm bảo bàn phím bị ẩn trước khi chuyển tab
      FocusManager.instance.primaryFocus?.unfocus();

      // Thêm delay để đảm bảo unfocus được hoàn tất
      await Future.delayed(const Duration(milliseconds: 100));

      return true;
    } else {
      // Stay on page
      print("Hủy chuyển tab, ở lại trang Profile");
      return false;
    }
  }

  // Add confirmation dialog when trying to navigate away
  Future<bool> _onWillPop() async {
    // Đảm bảo bàn phím bị ẩn
    FocusManager.instance.primaryFocus?.unfocus();

    if (!_isEdited) {
      return true;
    }

    // Show confirmation dialog
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Dữ liệu chưa được lưu'),
        content: const Text(
            'Bạn có thông tin chưa lưu. Bạn có chắc chắn muốn thoát không?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Ở lại'),
          ),
          TextButton(
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            onPressed: () {
              // Reset form về dữ liệu ban đầu khi chọn thoát
              _isEdited = false;
              _loadUserData();
              Navigator.of(context).pop(true);
            },
            child: const Text('Thoát'),
          ),
        ],
      ),
    );

    // Đảm bảo bàn phím không hiển thị khi quay lại
    FocusManager.instance.primaryFocus?.unfocus();
    return result ?? false;
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context); // Removed unnecessary ! operator

    if (_isLoading) {
      return const Scaffold(
        body: LoadingOverlay(),
      );
    }

    if (userData == null) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 60, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                l10n.errorLoadingProfile,
                style:
                    const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  ApiConfig.clearToken();
                  Navigator.pushAndRemoveUntil(
                    context,
                    MaterialPageRoute(builder: (context) => const pageLogin()),
                    (route) => false,
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30),
                  ),
                ),
                child: Text(
                  l10n.logout,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return WillPopScope(
      onWillPop: _onWillPop,
      child: GestureDetector(
        // Ẩn bàn phím khi chạm vào bất kỳ đâu trên màn hình
        onTap: () => FocusScope.of(context).unfocus(),
        child: Scaffold(
          backgroundColor: Colors.grey[50],
          appBar: AppBar(
            scrolledUnderElevation: 0,
            surfaceTintColor: Colors.white,
            backgroundColor: AppColors.primary,
            elevation: 0,
            toolbarHeight: 0, // No toolbar, just the status bar color
            automaticallyImplyLeading: false,
          ),
          body: ptr.SmartRefresher(
            controller: _refreshController,
            onRefresh: _onRefresh,
            child: CustomScrollView(
              slivers: [
                // Profile Header with better styling
                SliverToBoxAdapter(
                  child: Container(
                    padding: const EdgeInsets.fromLTRB(0, 0, 0, 30),
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: const BorderRadius.only(
                        bottomLeft: Radius.circular(30),
                        bottomRight: Radius.circular(30),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: SafeArea(
                      child: Column(
                        children: [
                          Padding(
                            padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  l10n.personalInfo,
                                  style: const TextStyle(
                                    fontSize: 22,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                                IconButton(
                                  onPressed: () {
                                    showDialog(
                                      context: context,
                                      builder: (context) => AlertDialog(
                                        title: Text(l10n.logout),
                                        content: Text(l10n.confirmLogout),
                                        actions: [
                                          TextButton(
                                            onPressed: () =>
                                                Navigator.pop(context),
                                            child: Text(l10n.cancel),
                                          ),
                                          TextButton(
                                            style: TextButton.styleFrom(
                                              foregroundColor: Colors.red,
                                            ),
                                            onPressed: () {
                                              ApiConfig.clearToken();
                                              Navigator.of(context).pop();
                                              Navigator.pushAndRemoveUntil(
                                                context,
                                                MaterialPageRoute(
                                                    builder: (context) =>
                                                        const pageLogin()),
                                                (route) => false,
                                              );
                                            },
                                            child: Text(l10n.logout),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                  icon: const Icon(Icons.logout,
                                      color: Colors.white),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 20),
                          GestureDetector(
                            onTap: _showImagePickerOptions,
                            child: Center(
                              child: Stack(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(4),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      shape: BoxShape.circle,
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.1),
                                          blurRadius: 8,
                                          offset: const Offset(0, 3),
                                        ),
                                      ],
                                    ),
                                    child: CircleAvatar(
                                      radius: 50,
                                      backgroundImage: _selectedImage != null
                                          ? FileImage(
                                              File(_selectedImage!.path))
                                          : (userData?["image"] != null
                                                  ? NetworkImage(
                                                      ApiConfig.baseUrl +
                                                          userData!["image"]!)
                                                  : const AssetImage(
                                                      'assets/images/default_avatar.png'))
                                              as ImageProvider,
                                    ),
                                  ),
                                  Positioned(
                                    right: 0,
                                    bottom: 0,
                                    child: Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: AppColors.primary,
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                            color: Colors.white, width: 3),
                                        boxShadow: [
                                          BoxShadow(
                                            color:
                                                Colors.black.withOpacity(0.1),
                                            blurRadius: 5,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: const Icon(
                                        Icons.camera_alt,
                                        color: Colors.white,
                                        size: 20,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: 15),
                          Text(
                            _nameController.text,
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 4),
                        ],
                      ),
                    ),
                  ),
                ),

                // Profile Content
                SliverToBoxAdapter(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Personal Information Section
                        _buildSectionHeader(Icons.language, l10n.details),
                        const SizedBox(height: 12),
                        EditableInfoItem(
                          icon: Icons.person,
                          label: l10n.fullName,
                          controller: _nameController,
                        ),
                        const SizedBox(height: 8),
                        EditableInfoItem(
                          icon: Icons.email,
                          label: l10n.email,
                          controller: _emailController,
                        ),
                        const SizedBox(height: 8),
                        EditableInfoItem(
                          icon: Icons.phone,
                          label: l10n.phoneNumber,
                          controller: _phoneController,
                          keyboardType: TextInputType.number,
                        ),
                        // Remove company name field by commenting it out
                        // const SizedBox(height: 8),
                        // EditableInfoItem(
                        //   icon: Icons.location_on,
                        //   label: l10n.companyname,
                        //   controller: _addressController,
                        // ),

                        const SizedBox(height: 24),

                        // Identity Information Section
                        _buildSectionHeader(Icons.badge, l10n.idCardInfo),
                        const SizedBox(height: 12),
                        EditableInfoItem(
                          icon: Icons.credit_card,
                          label: l10n.idCard,
                          controller: _cccdController,
                          readOnly: false,
                          keyboardType: TextInputType.number,
                        ),
                        const SizedBox(height: 8),
                        EditableInfoItem(
                          icon: Icons.cake,
                          label: l10n.birthday,
                          controller: _birthdayController,
                          readOnly: false,
                          onDateField: true,
                          onTap: () =>
                              _selectDate(context, _birthdayController),
                        ),
                        const SizedBox(height: 8),
                        EditableInfoItem(
                          icon: Icons.date_range,
                          label: l10n.issuedDate,
                          controller: _grantedDateController,
                          readOnly: false,
                          onDateField: true,
                          onTap: () =>
                              _selectDate(context, _grantedDateController),
                        ),
                        const SizedBox(height: 12),
                        _buildSectionHeader(Icons.language, l10n.language),
                        const SizedBox(height: 12),
                        Card(
                          elevation: 2,
                          margin: EdgeInsets.zero,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 4),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  l10n.language,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                DropdownButton<Locale>(
                                  value: Localizations.localeOf(context),
                                  underline: Container(),
                                  icon: const Icon(Icons.arrow_drop_down,
                                      color: AppColors.primary),
                                  items: L10n.all.map((locale) {
                                    return DropdownMenuItem(
                                      value: locale,
                                      child: Text(
                                        locale.languageCode == 'vi'
                                            ? l10n.vietnamese
                                            : l10n.english,
                                        style: const TextStyle(
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (Locale? newLocale) {
                                    if (newLocale != null) {
                                      context
                                          .read<LocaleProvider>()
                                          .setLocale(newLocale);
                                    }
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 24),

                        // Bank Information Section
                        BankInfoSection(
                          banks: banks,
                          onBanksChanged: (updatedBanks) {
                            setState(() {
                              banks = updatedBanks;
                            });
                          },
                        ),

                        const SizedBox(height: 24),

                        // Save Button
                        SizedBox(
                          width: double.infinity,
                          height: 50,
                          child: ElevatedButton.icon(
                            onPressed: _isSubmitting ? null : _saveChanges,
                            icon: _isSubmitting
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                          Colors.white),
                                    ),
                                  )
                                : const Icon(Icons.save, color: Colors.white),
                            label: Text(
                              _isSubmitting ? 'Đang lưu...' : l10n.saveChanges,
                              style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.primary,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 30),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Helper method to create consistent section headers
  Widget _buildSectionHeader(IconData icon, String title) {
    return Row(
      children: [
        Icon(
          icon,
          color: AppColors.primary,
          size: 22,
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }
}

// Widget cho trường thông tin có thể chỉnh sửa
class EditableInfoItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final TextEditingController controller;
  final bool readOnly;
  final List<DropdownMenuItem<String>>? dropdownItems;
  final Function(String?)? onDropdownChanged;
  final bool onDateField; // Add new property
  final VoidCallback? onTap; // Add onTap callback
  final TextInputType? keyboardType; // Add keyboard type parameter

  const EditableInfoItem({
    Key? key,
    required this.icon,
    required this.label,
    required this.controller,
    this.readOnly = false,
    this.dropdownItems,
    this.onDropdownChanged,
    this.onDateField = false, // Default to false
    this.onTap,
    this.keyboardType, // Add keyboard type parameter
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 1.5,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    icon,
                    color: AppColors.primary,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  label,
                  style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (readOnly)
                  const Padding(
                    padding: EdgeInsets.only(left: 8.0),
                    child: Icon(
                      Icons.lock_outline,
                      color: Colors.grey,
                      size: 12,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 6),
            if (dropdownItems != null)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6),
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: Colors.grey.withOpacity(0.3)),
                ),
                child: DropdownButtonFormField<String>(
                  value: controller.text.isEmpty ? null : controller.text,
                  items: dropdownItems,
                  onChanged: onDropdownChanged,
                  decoration: const InputDecoration(
                    isDense: true,
                    contentPadding: EdgeInsets.symmetric(vertical: 6),
                    border: InputBorder.none,
                  ),
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                  ),
                  dropdownColor: Colors.white,
                  icon: const Icon(Icons.arrow_drop_down,
                      color: AppColors.primary, size: 18),
                ),
              )
            else if (onDateField)
              // Cách xử lý đặc biệt cho trường ngày tháng
              GestureDetector(
                onTap: onTap,
                child: AbsorbPointer(
                  child: Container(
                    height: 38,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 0),
                    decoration: BoxDecoration(
                      color: readOnly
                          ? Colors.grey.withOpacity(0.05)
                          : Colors.white,
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(
                        color: readOnly
                            ? Colors.grey.withOpacity(0.2)
                            : AppColors.primary.withOpacity(0.3),
                      ),
                    ),
                    child: TextField(
                      controller: controller,
                      readOnly: true,
                      keyboardType: TextInputType.none, // Không hiện bàn phím
                      decoration: InputDecoration(
                        isDense: true,
                        contentPadding: const EdgeInsets.symmetric(vertical: 8),
                        border: InputBorder.none,
                        suffixIcon: const Icon(
                          Icons.calendar_today,
                          size: 16,
                          color: AppColors.primary,
                        ),
                        hintText: readOnly
                            ? 'Không có thông tin'
                            : 'Nhập ${label.toLowerCase()}',
                        hintStyle: TextStyle(
                          color: Colors.grey.withOpacity(0.7),
                          fontStyle: FontStyle.italic,
                          fontSize: 12,
                        ),
                      ),
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: readOnly ? Colors.grey.shade700 : Colors.black,
                      ),
                    ),
                  ),
                ),
              )
            else
              Container(
                height: 38,
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 0),
                decoration: BoxDecoration(
                  color:
                      readOnly ? Colors.grey.withOpacity(0.05) : Colors.white,
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(
                    color: readOnly
                        ? Colors.grey.withOpacity(0.2)
                        : AppColors.primary.withOpacity(0.3),
                  ),
                ),
                child: TextField(
                  controller: controller,
                  readOnly: readOnly,
                  keyboardType: keyboardType,
                  inputFormatters: keyboardType == TextInputType.number
                      ? [FilteringTextInputFormatter.digitsOnly]
                      : null,
                  decoration: InputDecoration(
                    isDense: true,
                    contentPadding: const EdgeInsets.symmetric(vertical: 8),
                    border: InputBorder.none,
                    hintText: readOnly
                        ? 'Không có thông tin'
                        : 'Nhập ${label.toLowerCase()}',
                    hintStyle: TextStyle(
                      color: Colors.grey.withOpacity(0.7),
                      fontStyle: FontStyle.italic,
                      fontSize: 12,
                    ),
                  ),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: readOnly ? Colors.grey.shade700 : Colors.black,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

// Add this new widget to handle bank info
class BankInfoSection extends StatefulWidget {
  final List<Map<String, String>> banks;
  final Function(List<Map<String, String>>) onBanksChanged;

  const BankInfoSection({
    Key? key,
    required this.banks,
    required this.onBanksChanged,
  }) : super(key: key);

  @override
  State<BankInfoSection> createState() => _BankInfoSectionState();
}

class _BankInfoSectionState extends State<BankInfoSection> {
  Future<void> _deleteBankAccount(int index, Map<String, String> bank) async {
    // Ẩn bàn phím
    FocusScope.of(context).unfocus();

    final l10n = AppLocalizations.of(context);

    // Show confirmation dialog first
    final shouldDelete = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.delete_outline, color: Colors.red),
            ),
            const SizedBox(width: 16),
            const Text(
              'Xác nhận xóa',
              style: TextStyle(fontSize: 18),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.confirmDeleteBank,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Colors.grey.shade100,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.account_balance,
                          size: 16, color: Colors.grey),
                      const SizedBox(width: 8),
                      Text(
                        bank['bankName'] ?? '',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text('Số TK: ${bank['bankNumber'] ?? ''}'),
                  Text('Chủ TK: ${bank['accountHolder'] ?? ''}'),
                ],
              ),
            ),
          ],
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('Xóa',
                style: TextStyle(fontWeight: FontWeight.bold)),
          ),
        ],
      ),
    );

    if (shouldDelete == true) {
      try {
        print('=== DELETE BANK DEBUG ===');
        print('Attempting to delete bank with id: ${bank['id']}');
        print('Bank details: $bank');

        final dio = Dio();
        final prefs = await SharedPreferences.getInstance();
        final token = prefs.getString('auth_token') ?? '';

        if (token.isEmpty) {
          throw Exception('Token không hợp lệ');
        }

        if (bank['id'] == null || bank['id']!.isEmpty) {
          throw Exception('Bank ID is missing or empty');
        }

        final response = await dio.delete(
          '${ApiConfig.baseUrl}${ApiEndpoints.deleteBank}/${bank['id']}',
          options: Options(
            headers: {
              'Authorization': 'Bearer $token',
              'Content-Type': 'application/json',
            },
          ),
        );

        print('Delete Response Status: ${response.statusCode}');
        print('Delete Response Data: ${response.data}');

        if (response.statusCode == 200) {
          // Update the list locally after successful deletion
          final updatedBanks = List<Map<String, String>>.from(widget.banks);
          updatedBanks.removeAt(index);
          widget.onBanksChanged(updatedBanks);

          print('Bank deleted successfully. Updated banks list: $updatedBanks');

          // Đảm bảo không có trường nào được focus sau khi xóa
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              FocusScope.of(context).unfocus();
              // Đặt unfocus lại một lần nữa sau một chút thời gian
              Future.delayed(const Duration(milliseconds: 100), () {
                FocusManager.instance.primaryFocus?.unfocus();
              });
            }
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(l10n.deleteBankSuccess),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          throw Exception('Không thể xóa tài khoản ngân hàng');
        }
      } catch (e) {
        print('Error deleting bank account: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${l10n.errorDeletingBank}: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.account_balance,
                color: AppColors.primary,
                size: 22,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              l10n.bankAccount,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        widget.banks.isEmpty
            ? Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.account_balance,
                      color: Colors.grey,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      l10n.noBankAccounts,
                      style: const TextStyle(
                        color: Colors.grey,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              )
            : ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: widget.banks.length,
                itemBuilder: (context, index) {
                  final bank = widget.banks[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 12),
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Colors.white,
                            AppColors.primary.withOpacity(0.05),
                          ],
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color:
                                            AppColors.primary.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: const Icon(
                                        Icons.account_balance,
                                        color: AppColors.primary,
                                        size: 20,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Text(
                                      bank['bankName'] ?? '',
                                      style: const TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                                IconButton(
                                  icon: const Icon(
                                    Icons.delete_outline,
                                    color: Colors.red,
                                    size: 22,
                                  ),
                                  tooltip: 'Xóa tài khoản',
                                  onPressed: () async {
                                    await _deleteBankAccount(index, bank);
                                  },
                                ),
                              ],
                            ),
                            const Divider(height: 24),
                            _buildBankInfoRow(
                              'Số tài khoản:',
                              bank['bankNumber'] ?? '',
                              Icons.credit_card,
                            ),
                            const SizedBox(height: 12),
                            _buildBankInfoRow(
                              'Chủ tài khoản:',
                              bank['accountHolder'] ?? '',
                              Icons.person,
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
      ],
    );
  }

  Widget _buildBankInfoRow(String label, String value, IconData icon) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.08),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(
            icon,
            size: 16,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(width: 12),
        SizedBox(
          width: 110,
          child: Text(
            label,
            style: const TextStyle(
              color: Colors.grey,
              fontWeight: FontWeight.w500,
              fontSize: 14,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 15,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
