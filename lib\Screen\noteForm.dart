import 'dart:async';
import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:du_an_flutter/Screen/notesListScreen.dart';
import 'package:du_an_flutter/page/pageHome.dart';
import 'package:du_an_flutter/page/pageHomeApprover.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:file_picker/file_picker.dart';
import 'package:du_an_flutter/constants/colors.dart';
import 'package:du_an_flutter/APIconfig/api_config.dart';
import 'package:du_an_flutter/APIconfig/api_endpoints.dart';
import 'package:du_an_flutter/l10n/app_localizations.dart';

class NoteForm extends StatefulWidget {
  const NoteForm({super.key});

  @override
  State<NoteForm> createState() => _NoteFormState();
}

class _NoteFormState extends State<NoteForm> {
  final _formKey = GlobalKey<FormState>();
  bool _isSubmitting = false;
  bool _hasChanges = false;
  bool _isCheckingChanges = false; // Prevent recursive change checking

  // Controllers for form fields
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _dateController = TextEditingController();

  // Selected date - default to tomorrow (future date)
  DateTime _selectedDate = DateTime.now().add(const Duration(days: 1));

  // Image selection
  List<PlatformFile> _selectedFiles = [];

  // Initial values for change detection - use lightweight comparison
  String _initialTitle = '';
  String _initialDescription = '';
  DateTime _initialDate = DateTime.now().add(const Duration(days: 1));
  int _initialFileCount = 0;
  List<String> _initialFileNames =
      []; // Store only names for lightweight comparison

  // Debouncing timer to prevent excessive change checking
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();

    // Set default date to tomorrow (future date only)
    _selectedDate = DateTime.now().add(const Duration(days: 1));
    _dateController.text = _formatDate(_selectedDate);

    // Add listeners for form change detection with debouncing
    _titleController.addListener(_debouncedCheckFormChanged);
    _descriptionController.addListener(_debouncedCheckFormChanged);

    // Save initial values
    _saveInitialValues();
  }

  // Simple date formatter
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  // Format date for API
  String _formatDateForAPI(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  // Validate if date is in the future (tomorrow or later)
  bool _isDateInFuture(DateTime date) {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    final dateOnly = DateTime(date.year, date.month, date.day);
    final tomorrowOnly = DateTime(tomorrow.year, tomorrow.month, tomorrow.day);
    return dateOnly.isAtSameMomentAs(tomorrowOnly) ||
        dateOnly.isAfter(tomorrowOnly);
  }

  // Get minimum allowed date (tomorrow)
  DateTime _getMinimumDate() {
    return DateTime.now().add(const Duration(days: 1));
  }

  @override
  void dispose() {
    // Cancel any pending debounce timer
    _debounceTimer?.cancel();

    _titleController.removeListener(_debouncedCheckFormChanged);
    _descriptionController.removeListener(_debouncedCheckFormChanged);

    _titleController.dispose();
    _descriptionController.dispose();
    _dateController.dispose();

    super.dispose();
  }

  // Save initial form values for change detection
  void _saveInitialValues() {
    _initialTitle = _titleController.text;
    _initialDescription = _descriptionController.text;
    _initialDate = _selectedDate;
    _initialFileCount = _selectedFiles.length;
    _initialFileNames = _selectedFiles.map((file) => file.name).toList();
    _hasChanges = false;
  }

  // Debounced wrapper for change detection to prevent excessive calls
  void _debouncedCheckFormChanged() {
    // Cancel any existing timer
    _debounceTimer?.cancel();

    // Set a new timer with a short delay to debounce rapid changes
    _debounceTimer = Timer(const Duration(milliseconds: 100), () {
      if (mounted) {
        _checkFormChanged();
      }
    });
  }

  // Check if form has changes - optimized for performance
  void _checkFormChanged() {
    // Prevent recursive calls and unnecessary checks
    if (_isCheckingChanges) return;
    _isCheckingChanges = true;

    try {
      // Quick checks first (most likely to change)
      bool hasChanges = false;

      // Check text fields
      if (_titleController.text != _initialTitle ||
          _descriptionController.text != _initialDescription) {
        hasChanges = true;
      }

      // Check date only if text fields haven't changed
      if (!hasChanges && _selectedDate != _initialDate) {
        hasChanges = true;
      }

      // Check files only if other fields haven't changed (most expensive check)
      if (!hasChanges) {
        hasChanges = _hasFileChanges();
      }

      // Only update state if the change status actually changed
      if (_hasChanges != hasChanges) {
        setState(() {
          _hasChanges = hasChanges;
        });
      }
    } finally {
      _isCheckingChanges = false;
    }
  }

  // Lightweight file comparison using only count and names
  bool _hasFileChanges() {
    // Quick count check first
    if (_selectedFiles.length != _initialFileCount) {
      return true;
    }

    // If counts match, check names (lightweight comparison)
    final currentFileNames = _selectedFiles.map((file) => file.name).toList();
    if (currentFileNames.length != _initialFileNames.length) {
      return true;
    }

    // Compare names only (avoid comparing file bytes which is expensive)
    for (int i = 0; i < currentFileNames.length; i++) {
      if (currentFileNames[i] != _initialFileNames[i]) {
        return true;
      }
    }

    return false;
  }

  // Helper method to extract organization ID from user data
  String? _extractOrganizationId(Map<String, dynamic> userData) {
    try {
      // First priority: Check currentOrganization (based on server response)
      if (userData['currentOrganization'] != null &&
          userData['currentOrganization']['id'] != null) {
        return userData['currentOrganization']['id'].toString();
      }

      // Second priority: Check user_organizations array for organizationId
      if (userData['user_organizations'] is List &&
          (userData['user_organizations'] as List).isNotEmpty) {
        var userOrg = userData['user_organizations'][0];
        if (userOrg is Map && userOrg['organizationId'] != null) {
          return userOrg['organizationId'].toString();
        }

        // Third priority: Check user_organizations[0].organization.id
        if (userOrg is Map && userOrg['organization'] is Map) {
          var orgId = userOrg['organization']['id'];
          if (orgId != null) {
            return orgId.toString();
          }
        }
      }

      // Fourth priority: Check organization field
      if (userData['organization'] != null &&
          userData['organization']['id'] != null) {
        return userData['organization']['id'].toString();
      }

      // Fifth priority: Check direct organizationId field
      if (userData['organizationId'] != null) {
        return userData['organizationId'].toString();
      }

      print('Warning: Could not extract organization ID from user data');
      print('Available keys: ${userData.keys.toList()}');
      return null;
    } catch (e) {
      print('Error extracting organization ID: $e');
      return null;
    }
  }

  // Date picker function with future date validation
  Future<void> _selectDate() async {
    final DateTime minimumDate = _getMinimumDate();
    final DateTime maxDate =
        DateTime.now().add(const Duration(days: 365 * 2)); // 2 years from now

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          _selectedDate.isBefore(minimumDate) ? minimumDate : _selectedDate,
      firstDate: minimumDate, // Only allow future dates (tomorrow and beyond)
      lastDate: maxDate,
      locale: const Locale('vi', 'VN'),
      helpText: AppLocalizations.of(context).selectNotificationDate,
      confirmText: AppLocalizations.of(context).select,
      cancelText: AppLocalizations.of(context).cancel,
      errorFormatText: AppLocalizations.of(context).invalidDateFormat,
      errorInvalidText: AppLocalizations.of(context).invalidDate,
      fieldLabelText: AppLocalizations.of(context).enterDate,
      fieldHintText: 'dd/mm/yyyy',
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      // Additional validation using helper method
      if (!_isDateInFuture(picked)) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                AppLocalizations.of(context).futureDateOnly,
                style: const TextStyle(color: Colors.white),
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
              action: SnackBarAction(
                label: AppLocalizations.of(context).close,
                textColor: Colors.white,
                onPressed: () {
                  ScaffoldMessenger.of(context).hideCurrentSnackBar();
                },
              ),
            ),
          );
        }
        return; // Don't update the date if it's invalid
      }

      // Update the selected date if it's valid
      if (picked != _selectedDate) {
        setState(() {
          _selectedDate = picked;
          _dateController.text = _formatDate(picked);
        });
        // Trigger debounced change detection
        _debouncedCheckFormChanged();
      }
    }
  }

  // Image picker function
  Future<void> _pickFiles() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['jpg', 'jpeg', 'png'],
        allowMultiple: true,
        withData: true, // Required for file upload
      );

      if (result != null) {
        setState(() {
          // Add new files to existing list
          _selectedFiles = [
            ..._selectedFiles,
            ...result.files,
          ];
          _checkFormChanged();
        });
        // Trigger debounced change detection
        _debouncedCheckFormChanged();
      }
    } catch (e) {
      print('Error picking files: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).cannotSelectImage),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Build file preview widget
  Widget _buildFilePreview(PlatformFile file) {
    if (file.bytes != null) {
      final extension = file.name.split('.').last.toLowerCase();
      if (['jpg', 'jpeg', 'png'].contains(extension)) {
        return Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: Image.memory(
              file.bytes!,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return const Icon(
                  Icons.broken_image,
                  color: Colors.red,
                  size: 24,
                );
              },
            ),
          ),
        );
      }
    }

    // Default icon for non-image files or when preview fails
    return Icon(
      Icons.image,
      color: AppColors.primary,
      size: 40,
    );
  }

  // Build image card with preview and remove button
  Widget _buildImageCard(PlatformFile file) {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Stack(
        children: [
          // Image preview
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: file.bytes != null
                  ? Image.memory(
                      file.bytes!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey[100],
                          child: Icon(
                            Icons.broken_image,
                            color: Colors.grey[400],
                            size: 24,
                          ),
                        );
                      },
                    )
                  : Container(
                      color: Colors.grey[100],
                      child: Icon(
                        Icons.image,
                        color: Colors.grey[400],
                        size: 24,
                      ),
                    ),
            ),
          ),
          // Remove button
          Positioned(
            top: 4,
            right: 4,
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedFiles.remove(file);
                });
                // Trigger debounced change detection
                _debouncedCheckFormChanged();
              },
              child: Container(
                width: 20,
                height: 20,
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 14,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Submit form function
  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content:
              Text(AppLocalizations.of(context).pleaseCompleteRequiredFields),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Store localizations before async operations
    final localizations = AppLocalizations.of(context);

    setState(() => _isSubmitting = true);

    try {
      final dio = Dio();
      dio.options.connectTimeout = const Duration(seconds: 30);
      dio.options.receiveTimeout = const Duration(seconds: 30);

      // Get auth token and user data
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? '';
      final userDataString = prefs.getString('user_data');

      if (token.isEmpty) {
        throw Exception(localizations.invalidTokenPleaseLogin);
      }

      if (userDataString == null) {
        throw Exception(localizations.userNotFound);
      }

      // Parse user data to get userId and organizationId
      final userData = jsonDecode(userDataString);

      final userId = userData['id']?.toString();
      final organizationId = _extractOrganizationId(userData);

      if (userId == null || userId.isEmpty) {
        throw Exception(localizations.userIdNotFound);
      }

      if (organizationId == null || organizationId.isEmpty) {
        throw Exception(localizations.organizationInfoNotFound);
      }

      // Prepare form data for multipart upload
      final formData = FormData();

      // Add text fields including required userId and organizationId
      formData.fields.addAll([
        MapEntry('title', _titleController.text.trim()),
        MapEntry('description', _descriptionController.text.trim()),
        MapEntry('notiDate', _formatDateForAPI(_selectedDate)),
        MapEntry('userId', userId),
        MapEntry('organizationId', organizationId),
      ]);

      // Add image files if any
      for (var file in _selectedFiles) {
        try {
          if (file.bytes != null) {
            formData.files.add(
              MapEntry(
                'image',
                MultipartFile.fromBytes(file.bytes!, filename: file.name),
              ),
            );
          }
        } catch (fileError) {
          print('Error processing file ${file.name}: $fileError');
          // Continue with other files instead of failing completely
        }
      }

      // Configure Dio headers for multipart
      dio.options.headers = {
        'Authorization': 'Bearer $token',
        'Content-Type': 'multipart/form-data',
      };

      // Use the correct API endpoint
      final String url = '${ApiConfig.baseUrl}${ApiEndpoints.createNote}';

      print('=== NOTE SUBMISSION DEBUG ===');
      print('URL: $url');
      print('Form fields:');
      formData.fields.forEach((field) {
        print('${field.key}: ${field.value}');
      });
      print('Files count: ${formData.files.length}');

      final response = await dio.post(
        url,
        data: formData,
        onSendProgress: (sent, total) {
          if (total != -1) {
            print(
                'Upload Progress: ${(sent / total * 100).toStringAsFixed(0)}%');
          }
        },
      );

      print('Response Status: ${response.statusCode}');
      print('Response Data: ${response.data}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (mounted) {
          // Hide keyboard
          FocusScope.of(context).unfocus();

          // Show success dialog
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (BuildContext dialogContext) {
              return AlertDialog(
                title: Text(localizations.success),
                content: Text(localizations.noteSavedSuccess),
                actions: [
                  TextButton(
                    onPressed: () async {
                      Navigator.of(dialogContext).pop(); // Close dialog first

                      if (!mounted) return;

                      // Get user role to determine which main page to navigate to
                      final prefs = await SharedPreferences.getInstance();
                      final userDataString = prefs.getString('user_data');
                      String userRole = 'Submitter'; // Default role

                      if (userDataString != null) {
                        try {
                          final userData = jsonDecode(userDataString);
                          userRole = userData['role'] ?? 'Submitter';
                        } catch (e) {
                          print('Error parsing user role: $e');
                        }
                      }

                      if (!mounted) return;

                      // Navigate back to main app preserving bottom navigation
                      if (userRole == 'Approver') {
                        // For Approver role - notes are not in bottom nav, go to home
                        Navigator.pushAndRemoveUntil(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const HomePageApprover(),
                          ),
                          (route) => route.isFirst,
                        );
                      } else {
                        // For Submitter role - navigate to main app with notes tab selected
                        Navigator.pushAndRemoveUntil(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                const HomePage(initialTabIndex: 3),
                          ),
                          (route) => route.isFirst,
                        );
                      }
                    },
                    child: const Text('OK'),
                  ),
                ],
              );
            },
          );
        }
      } else {
        throw Exception('${localizations.serverError}: ${response.statusCode}');
      }
    } catch (e) {
      print('Error submitting note: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${localizations.errorSavingNote}: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  // Exit confirmation dialog - optimized to prevent freezing
  Future<bool> _showExitConfirmationDialog() async {
    // Quick return if no changes to avoid unnecessary dialog
    if (!_hasChanges) return true;

    // Store localizations before async operation to prevent context issues
    if (!mounted) return false;
    final localizations = AppLocalizations.of(context);

    try {
      final result = await showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (dialogContext) => AlertDialog(
          title: Text(localizations.confirm),
          content: Text(localizations.unsavedChangesConfirm),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(false),
              child: Text(localizations.no),
            ),
            TextButton(
              onPressed: () {
                Navigator.pushAndRemoveUntil(
                  context,
                  MaterialPageRoute(builder: (context) => const HomePage()),
                  (Route<dynamic> route) => false, // Xoá hết các màn hình trước
                );
              },
              child: Text(localizations.yes),
            ),
          ],
        ),
      );

      return result ?? false;
    } catch (e) {
      // Handle any dialog errors gracefully
      print('Error showing exit confirmation dialog: $e');
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (_isSubmitting) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context).processingPleaseWait),
              backgroundColor: Colors.orange,
            ),
          );
          return false;
        }

        _checkFormChanged();

        final canPop = await _showExitConfirmationDialog();
        return canPop;
      },
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          scrolledUnderElevation: 0,
          surfaceTintColor: Colors.white,
          backgroundColor: Colors.white,
          elevation: 0,
          title: Text(
            AppLocalizations.of(context).createNote,
            style: const TextStyle(
              color: Colors.black,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(1.0),
            child: Container(
              color: Colors.grey.shade600,
              height: 1.0,
            ),
          ),
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocalizations.of(context).noteInformation,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),

                // Title field
                TextFormField(
                  controller: _titleController,
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context).noteTitleLabel,
                    hintText: AppLocalizations.of(context).enterNoteTitle,
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.title_outlined),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return AppLocalizations.of(context).pleaseEnterNoteTitle;
                    }
                    if (value.trim().length < 3) {
                      return AppLocalizations.of(context).titleMinLength;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Date picker field with future date validation
                TextFormField(
                  controller: _dateController,
                  decoration: InputDecoration(
                    labelText:
                        AppLocalizations.of(context).notificationDateLabel,
                    hintText: AppLocalizations.of(context).selectDateFutureOnly,
                    helperText: AppLocalizations.of(context).futureDateHelper,
                    helperStyle: TextStyle(
                      color: Colors.orange[700],
                      fontSize: 12,
                    ),
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.calendar_today_outlined),
                    suffixIcon: const Icon(Icons.arrow_drop_down),
                  ),
                  readOnly: true,
                  onTap: _selectDate,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return AppLocalizations.of(context)
                          .pleaseSelectNotificationDate;
                    }

                    // Validate that selected date is in the future using helper method
                    if (!_isDateInFuture(_selectedDate)) {
                      return AppLocalizations.of(context).futureDateValidation;
                    }

                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Description field
                TextFormField(
                  controller: _descriptionController,
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context).noteContentLabel,
                    hintText: AppLocalizations.of(context).enterNoteContent,
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.description_outlined),
                    alignLabelWithHint: true,
                  ),
                  maxLines: 5,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return AppLocalizations.of(context)
                          .pleaseEnterNoteContent;
                    }

                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Image attachment section
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.image_outlined,
                            color: Colors.grey[600],
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            AppLocalizations.of(context).attachedImages,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey[700],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),

                      // Add image button
                      SizedBox(
                        width: double.infinity,
                        child: OutlinedButton.icon(
                          onPressed: _pickFiles,
                          icon: const Icon(Icons.add_photo_alternate_outlined),
                          label:
                              Text(AppLocalizations.of(context).selectImages),
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            side: BorderSide(color: AppColors.primary),
                            foregroundColor: AppColors.primary,
                          ),
                        ),
                      ),

                      // Display selected images
                      if (_selectedFiles.isNotEmpty) ...[
                        const SizedBox(height: 12),
                        Text(
                          AppLocalizations.of(context).selectedImages,
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                        const SizedBox(height: 8),
                        // Grid of selected images
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: _selectedFiles
                              .map((file) => _buildImageCard(file))
                              .toList(),
                        ),
                      ],
                    ],
                  ),
                ),
                const SizedBox(height: 24),

                // Action buttons
                Row(
                  children: [
                    // Clear button

                    // Save button
                    Expanded(
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        onPressed: _isSubmitting ? null : _submitForm,
                        child: _isSubmitting
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white),
                                ),
                              )
                            : Text(
                                AppLocalizations.of(context).saveNote,
                                style: const TextStyle(fontSize: 16),
                              ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
