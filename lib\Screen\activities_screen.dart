// Tạo file mới: lib/widgets/activity_card.dart
import 'package:du_an_flutter/APIconfig/api_config.dart';
import 'package:du_an_flutter/APIconfig/api_endpoints.dart';
import 'package:du_an_flutter/Screen/invoiceForm.dart';
import 'package:du_an_flutter/Screen/invoice_detail.dart';
import 'package:du_an_flutter/Screen/paymentForm.dart';
import 'package:du_an_flutter/Screen/payment_detail.dart';
import 'package:du_an_flutter/Screen/tax_detail.dart';
import 'package:du_an_flutter/services/approval_service.dart';

import 'package:flutter/material.dart';
import 'package:du_an_flutter/model/activity_item.dart';
import 'package:flutter/services.dart';
import 'package:du_an_flutter/l10n/app_localizations.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:du_an_flutter/Screen/pendingSubmitter.dart';

import 'package:intl/intl.dart';
import 'package:dio/dio.dart';

class ActivityScreen extends StatelessWidget {
  final ActivityItem item;
  final VoidCallback? onRefresh;
  final VoidCallback? onStatusChanged;
  static const _borderRadius = 12.0;
  static const _iconSize = 16.0;
  static const _spacingHeight = 8.0;
  static const _spacingWidth = 4.0;
  static const _padding = 16.0;

  const ActivityScreen({
    Key? key,
    required this.item,
    this.onRefresh,
    this.onStatusChanged,
  }) : super(key: key);

  String _formatNumber(String number) {
    // Xóa bỏ các dấu phân cách hiện có (nếu có)
    String cleanNumber = number.replaceAll(RegExp(r'[,.]'), '');

    // Chuyển đổi sang số và format
    try {
      final value = int.parse(cleanNumber);
      // Sử dụng NumberFormat với pattern tùy chỉnh để dùng dấu chấm làm dấu phân cách
      final format = NumberFormat('#,###', 'en_US');
      // Thay thế dấu phẩy bằng dấu chấm trong kết quả
      return format.format(value).replaceAll(',', '.');
    } catch (e) {
      return number; // Trả về giá trị gốc nếu không thể parse
    }
  }

  String _getCurrencySymbol(String currencyCode) {
    const symbols = {
      'VND': '₫',
      'USD': '\$',
      'EUR': '€',
      'GBP': '£',
      'JPY': '¥',
      'CNY': '¥',
      'KRW': '₩',
      'CHF': 'Fr',
    };
    return symbols[currencyCode] ?? currencyCode;
  }

  String _formatDate(String dateString) {
    final date = DateTime.parse(dateString);
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<bool> _isApprover() async {
    final prefs = await SharedPreferences.getInstance();
    final userRole = prefs.getString('user_role');
    print('DEBUG - User role from SharedPreferences: $userRole');
    return userRole == 'Approver';
  }

  Color _getStatusColor() {
    switch (item.status.toLowerCase()) {
      case 'draft':
        return Colors.grey[300]!; // Light grey
      case 'cancel':
        return Colors.grey[700]!; // Dark grey
      case 'fail':
        return Colors.red[400]!; // Red
      case 'pendingaccountant':
        return Colors.amber[400]!; // Yellow
      case 'pendingapprover':
        return Colors.lightBlue[300]!; // Light blue
      case 'approved':
        return Colors.lightGreen[400]!; // Light green
      default:
        return Colors.grey[300]!; // Default light grey
    }
  }

  String _getStatusText(AppLocalizations l10n) {
    switch (item.status.toLowerCase()) {
      case 'draft':
        return 'Draft';
      case 'fail':
        return 'Failed';
      case 'approved':
        return 'Approved';
      case 'pendingapprover':
        return 'Pending Approver';
      case 'pendingaccountant':
        return 'Pending Accountant';
      case 'cancel':
        return 'Cancel';
      default:
        return 'Unknown';
    }
  }

  void _handleEdit(BuildContext context) async {
    if (item.categoryId == 1) {
      // For Payment form (categoryId = 1)
      final result = await Navigator.push<bool>(
        context,
        MaterialPageRoute(
          builder: (context) => PaymentForm(
            editItem: item,
            onSubmitSuccess: () async {
              onRefresh?.call();

              // Trigger PendingSubmitterScreen refresh for automatic data update
              try {
                await PendingSubmitterScreenState.triggerRefresh();
              } catch (e) {
                print('Error triggering PendingSubmitterScreen refresh: $e');
              }

              // Pop back to pendingSubmitter after successful edit
              onStatusChanged?.call();
            },
          ),
        ),
      );

      // Handle result from PaymentForm
      _handleEditResult(result);
    } else if (item.categoryId == 2) {
      // For Invoice form (categoryId = 2)
      final result = await Navigator.push<bool>(
        context,
        MaterialPageRoute(
          builder: (context) => InvoiceForm(
            editData: item,
            formTitle: 'Chỉnh sửa hóa đơn',
            onSubmitSuccess: () async {
              onRefresh?.call();

              // Trigger PendingSubmitterScreen refresh for automatic data update
              try {
                await PendingSubmitterScreenState.triggerRefresh();
              } catch (e) {
                print('Error triggering PendingSubmitterScreen refresh: $e');
              }

              // Pop back to pendingSubmitter after successful edit
              onStatusChanged?.call();
            },
          ),
        ),
      );

      // Handle result from InvoiceForm
      _handleEditResult(result);
    }
  }

  /// Handles the result returned from edit forms
  /// [changesMade] - true if user made changes, false if no changes, null if submitted successfully
  void _handleEditResult(bool? changesMade) {
    if (changesMade == null) {
      // Form was submitted successfully - onSubmitSuccess callback already handled navigation
      print('Edit form submitted successfully');
    } else if (changesMade == true) {
      // User made changes but didn't submit - they were prompted with confirmation
      print('User made changes but exited edit form');
    } else {
      // User made no changes - exited cleanly without confirmation
      print('User exited edit form without making changes');
    }

    // Note: No additional navigation needed here as forms handle their own navigation
    // The onSubmitSuccess callback handles navigation for successful submissions
    // The forms handle confirmation dialogs for unsaved changes internally
  }

  void _handleDelete(BuildContext context, AppLocalizations l10n) async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.confirmDelete),
        content: Text('${l10n.confirmDelete}: "${item.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(l10n.cancel),
          ),
          TextButton(
            onPressed: () async {
              try {
                // Di chuyển Navigator.pop(context) xuống sau khi API call thành công
                final dio = Dio();
                final prefs = await SharedPreferences.getInstance();
                final token = prefs.getString('auth_token') ?? '';

                final response = await dio.delete(
                  '${ApiConfig.baseUrl}${ApiEndpoints.deleteForm}/${item.id}',
                  options: Options(
                    headers: {
                      'Authorization': 'Bearer $token',
                      'Content-Type': 'application/json',
                    },
                  ),
                );

                if (response.statusCode == 200) {
                  Navigator.pop(context); // Đóng confirm dialog
                  if (context.mounted) {
                    showDialog(
                      context: context,
                      barrierDismissible: false,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          title: const Text('Success'),
                          content: const Text('Item deleted successfully'),
                          actions: [
                            TextButton(
                              onPressed: () async {
                                Navigator.pop(context);
                                onRefresh?.call();

                                // Trigger PendingSubmitterScreen refresh for automatic data update
                                try {
                                  await PendingSubmitterScreenState
                                      .triggerRefresh();
                                } catch (e) {
                                  print(
                                      'Error triggering PendingSubmitterScreen refresh: $e');
                                }

                                // Pop back to pendingSubmitter after successful delete
                                onStatusChanged?.call();
                              },
                              child: const Text('OK'),
                            ),
                          ],
                        );
                      },
                    );
                  }
                }
              } catch (e) {
                Navigator.pop(context); // Đóng confirm dialog nếu có lỗi
                print('Error deleting item: $e');
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Error: ${e.toString()}')),
                  );
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(l10n.delete),
          ),
        ],
      ),
    );
  }

  void _handleApprove(BuildContext context) {
    ApprovalService.showApprovalDialog(
      context: context,
      itemId: item.id,
      onSuccess: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Request approved successfully')),
        );
        onRefresh?.call();
      },
    );
  }

  void _handleReject(BuildContext context) {
    ApprovalService.showRejectDialog(
      context: context,
      itemId: item.id,
      onSuccess: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Request rejected successfully')),
        );
        onRefresh?.call();
      },
    );
  }

  void _handleCancel(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Cancel Request'),
          content: const Text('Are you sure you want to cancel this request?'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(dialogContext);
              },
              child: const Text('No'),
            ),
            TextButton(
              onPressed: () async {
                try {
                  Navigator.pop(dialogContext);

                  final dio = Dio();
                  final prefs = await SharedPreferences.getInstance();
                  final token = prefs.getString('auth_token') ?? '';

                  final response = await dio.post(
                    '${ApiConfig.baseUrl}${ApiEndpoints.updateFormSubmitter}/${item.id}',
                    options: Options(
                      headers: {
                        'Authorization': 'Bearer $token',
                        'Content-Type': 'application/json',
                      },
                    ),
                    data: {
                      'status': 'cancel',
                    },
                  );

                  if (response.statusCode == 200 && context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text('Request cancelled successfully')),
                    );
                    onRefresh?.call();

                    // Trigger PendingSubmitterScreen refresh for automatic data update
                    try {
                      await PendingSubmitterScreenState.triggerRefresh();
                    } catch (e) {
                      print(
                          'Error triggering PendingSubmitterScreen refresh: $e');
                    }

                    // Pop back to pendingSubmitter after successful status change
                    onStatusChanged?.call();
                  }
                } catch (e) {
                  print('Error cancelling item: $e');
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Error: ${e.toString()}')),
                    );
                  }
                }
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Yes'),
            ),
          ],
        );
      },
    );
  }

  void _handleSend(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Send Request'),
          content: const Text('Are you sure you want to send this request?'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(dialogContext);
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                try {
                  Navigator.pop(dialogContext);

                  final dio = Dio();
                  final prefs = await SharedPreferences.getInstance();
                  final token = prefs.getString('auth_token') ?? '';

                  final response = await dio.post(
                    '${ApiConfig.baseUrl}${ApiEndpoints.updateFormSubmitter}/${item.id}',
                    options: Options(
                      headers: {
                        'Authorization': 'Bearer $token',
                        'Content-Type': 'application/json',
                      },
                    ),
                    data: {
                      'status': 'pendingaccountant',
                    },
                  );

                  if (response.statusCode == 200 && context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text('Request sent successfully')),
                    );
                    onRefresh?.call();

                    // Trigger PendingSubmitterScreen refresh for automatic data update
                    try {
                      await PendingSubmitterScreenState.triggerRefresh();
                    } catch (e) {
                      print(
                          'Error triggering PendingSubmitterScreen refresh: $e');
                    }

                    // Pop back to pendingSubmitter after successful status change
                    onStatusChanged?.call();
                  }
                } catch (e) {
                  print('Error sending item: $e');
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Error: ${e.toString()}')),
                    );
                  }
                }
              },
              style: TextButton.styleFrom(foregroundColor: Colors.blue),
              child: const Text('Send'),
            ),
          ],
        );
      },
    );
  }

  void _handleRestore(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Restore to Draft'),
          content: const Text(
              'Are you sure you want to restore this item to draft?'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(dialogContext);
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                try {
                  Navigator.pop(dialogContext);

                  final dio = Dio();
                  final prefs = await SharedPreferences.getInstance();
                  final token = prefs.getString('auth_token') ?? '';

                  final response = await dio.post(
                    '${ApiConfig.baseUrl}${ApiEndpoints.updateFormSubmitter}/${item.id}',
                    options: Options(
                      headers: {
                        'Authorization': 'Bearer $token',
                        'Content-Type': 'application/json',
                      },
                    ),
                    data: {
                      'status': 'draft',
                    },
                  );

                  if (response.statusCode == 200 && context.mounted) {
                    // Show success popup
                    showDialog(
                      context: context,
                      barrierDismissible: false,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          title: const Text('Success'),
                          content:
                              const Text('Item restored to draft successfully'),
                          actions: [
                            TextButton(
                              onPressed: () async {
                                Navigator.pop(context);
                                onRefresh?.call(); // Refresh the list

                                // Trigger PendingSubmitterScreen refresh for automatic data update
                                try {
                                  await PendingSubmitterScreenState
                                      .triggerRefresh();
                                } catch (e) {
                                  print(
                                      'Error triggering PendingSubmitterScreen refresh: $e');
                                }

                                // Pop back to pendingSubmitter after successful status change
                                onStatusChanged?.call();
                              },
                              child: const Text('OK'),
                            ),
                          ],
                        );
                      },
                    );
                  }
                } catch (e) {
                  print('Error restoring item: $e');
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Error: ${e.toString()}')),
                    );
                  }
                }
              },
              style: TextButton.styleFrom(foregroundColor: Colors.blue),
              child: const Text('Restore'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildStatusBadge(AppLocalizations l10n) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusColor(),
        borderRadius: BorderRadius.circular(_borderRadius),
      ),
      child: Text(
        _getStatusText(l10n),
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  List<PopupMenuEntry<String>> _buildMenuItems(
      bool isApprover, AppLocalizations l10n) {
    final status = item.status.toLowerCase();

    print(
        'DEBUG - Building menu items: isApprover=$isApprover, status=$status, itemId=${item.id}');

    // For Approver role - NO menu items since settings icon is hidden
    if (isApprover) {
      print(
          'DEBUG - User is Approver, no menu items shown (settings icon hidden)');
      return []; // No menu items for Approver users
    }

    // For non-Approver users (submitters) - show menu items for specific statuses
    if (!['draft', 'fail', 'cancel'].contains(status)) {
      return [];
    }

    // For draft and fail status
    if (status == 'draft' || status == 'fail') {
      return [
        PopupMenuItem(
          value: 'edit',
          child: _buildMenuItem(
            icon: const Icon(Icons.edit),
            text: 'Edit',
          ),
        ),
        PopupMenuItem(
          value: 'send',
          child: _buildMenuItem(
            icon: const Icon(Icons.send, color: Colors.blue),
            text: 'Send',
            textColor: Colors.blue,
          ),
        ),
        PopupMenuItem(
          value: 'cancel',
          child: _buildMenuItem(
            icon: const Icon(Icons.cancel_outlined, color: Colors.grey),
            text: 'Cancel',
            textColor: Colors.grey,
          ),
        ),
      ];
    }

    // For cancel status
    if (status == 'cancel') {
      return [
        PopupMenuItem(
          value: 'delete',
          child: _buildMenuItem(
            icon: const Icon(Icons.delete, color: Colors.red),
            text: 'Delete',
            textColor: Colors.red,
          ),
        ),
        PopupMenuItem(
          value: 'restore',
          child: _buildMenuItem(
            icon: const Icon(Icons.restore, color: Colors.blue),
            text: 'Restore Draft',
            textColor: Colors.blue,
          ),
        ),
      ];
    }

    return [];
  }

  Widget _buildMenuItem({
    required Icon icon,
    required String text,
    Color? textColor,
  }) {
    return Row(
      children: [
        icon,
        const SizedBox(width: _spacingWidth),
        Text(text, style: TextStyle(color: textColor)),
      ],
    );
  }

  Widget _buildHeader(
      BuildContext context, bool isApprover, AppLocalizations l10n) {
    final status = item.status.toLowerCase();

    // For Approver role - NEVER show settings icon (as per requirement)
    if (isApprover) {
      return Padding(
        padding: const EdgeInsets.only(
          left: _padding,
          right: _padding,
          top: _padding,
          bottom: _spacingHeight, // Reduced bottom padding to minimize gap
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildStatusBadge(l10n),
            // No settings icon for Approver users
          ],
        ),
      );
    }

    // For non-Approver users (submitters) - show settings for draft, fail, and cancel statuses
    final showSettings = ['draft', 'fail', 'cancel'].contains(status);

    print(
        'DEBUG - Building header: isApprover=$isApprover, status=$status, showSettings=$showSettings');

    return Padding(
      padding: const EdgeInsets.only(
        left: _padding,
        right: _padding,
        top: _padding,
        bottom: _spacingHeight, // Reduced bottom padding to minimize gap
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildStatusBadge(l10n),
          if (showSettings) // Show settings icon only for non-Approver users
            PopupMenuButton<String>(
              icon: const Icon(Icons.settings, color: Colors.grey, size: 24),
              itemBuilder: (context) => _buildMenuItems(isApprover, l10n),
              onSelected: (value) {
                switch (value) {
                  case 'edit':
                    _handleEdit(context);
                    break;
                  case 'delete':
                    _handleDelete(context, l10n);
                    break;
                  case 'cancel':
                    _handleCancel(context);
                    break;
                  case 'send':
                    _handleSend(context);
                    break;
                  case 'restore':
                    _handleRestore(context);
                    break;
                }
              },
            ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context, AppLocalizations l10n) {
    return InkWell(
      onTap: () {
        if (item.category.id == 1) {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => PaymentDetail(item: item)),
          );
        } else if (item.category.id == 2) {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => InvoiceDetail(item: item)),
          );
        } else if (item.category.id == 3) {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => TaxDetail(item: item)),
          );
        }
      },
      child: Padding(
        padding: const EdgeInsets.only(
          left: _padding,
          right: _padding,
          top: _spacingHeight, // Reduced top padding to minimize gap
          bottom: _padding,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              item.title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: _spacingHeight),
            if (item.attachments != null && item.attachments!.isNotEmpty) ...[
              _buildInfoRow(
                icon: Icons.attach_file,
                text: '${item.attachments!.length} ${l10n.attachments}',
              ),
              const SizedBox(height: _spacingHeight),
            ],
            _buildPriceRow(),
            const SizedBox(height: _spacingHeight),
            _buildInfoRow(
              icon: Icons.category,
              text: '${l10n.category}: ',
              suffix: item.category.name,
              color: null,
              isBold: true, // This makes the category label bold
            ),
            const SizedBox(height: _spacingHeight),
            _buildInfoRow(
              icon: Icons.calendar_today,
              text: '${l10n.date}: ${_formatDate(item.createdAt)}',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String text,
    String? suffix,
    Color? color, // When color is null, it defaults to black
    bool isBold = false,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: _iconSize,
          color: color, // Icon will be default color (black) when color is null
        ),
        const SizedBox(width: _spacingWidth),
        Text(
          text,
          style: TextStyle(
            color:
                color, // Text will be default color (black) when color is null
            fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        if (suffix != null)
          Text(
            suffix,
            style: TextStyle(
              color: color,
              fontWeight: isBold
                  ? FontWeight.normal
                  : FontWeight.normal, // Keep suffix normal weight
            ),
          ),
      ],
    );
  }

  Widget _buildPriceRow() {
    return Row(
      children: [
        Text(
          _getCurrencySymbol(item.currency),
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(width: _spacingWidth),
        if (item.category.name.toLowerCase() == 'invoice') ...[
          Text(
            '${_formatNumber(item.totalPrice ?? '0')} ${item.currency}',
          ),
        ] else ...[
          Text(
            '${_formatNumber(item.price)} ${item.currency}',
          ),
        ],
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return FutureBuilder<bool>(
      future: _isApprover(),
      builder: (context, snapshot) {
        final isApproverRole = snapshot.data ?? false;
        print(
            'DEBUG - FutureBuilder result: isApproverRole=$isApproverRole, hasData=${snapshot.hasData}, hasError=${snapshot.hasError}, error=${snapshot.error}');

        return Card(
          color: Colors.grey[50], // Very slight grey tint instead of pure white
          elevation: 4,
          margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.0),
            side: BorderSide(
              color: Colors.grey.shade400, // Darker border for more contrast
              width: 1.0,
            ),
          ),
          shadowColor: Colors.black54,
          child: Column(
            children: [
              _buildHeader(context, isApproverRole, l10n),
              // Add a subtle divider between header and content
              Container(
                height: 1,
                margin: const EdgeInsets.symmetric(horizontal: _padding),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.transparent,
                      Colors.grey.shade300,
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
              _buildContent(context, l10n),
            ],
          ),
        );
      },
    );
  }
}
